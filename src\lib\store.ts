// This file is deprecated - use src/store/index.ts instead
// Keeping for backward compatibility
export { store } from "@/store"
export type { RootState, AppDispatch } from "@/store"

// Re-export hooks for backward compatibility
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux'
import type { RootState, AppDispatch } from "@/store"

export const useAppDispatch = () => useDispatch<AppDispatch>()
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector
