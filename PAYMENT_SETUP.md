# Payment System Setup Guide

## Razorpay Integration

The Ad Network platform now includes a complete payment system using Razorpay for advertisers to add funds to their wallet.

### Features Implemented

1. **Wallet System**
   - Real-time balance tracking
   - Transaction history
   - Payment order management

2. **Razorpay Integration**
   - Secure payment processing
   - Payment verification
   - Automatic balance updates

3. **User Experience**
   - Insufficient balance detection
   - Helpful error messages with "Add Funds" action
   - Quick amount selection (₹500, ₹1000, ₹2000, ₹5000)
   - Real-time balance updates

### Setup Instructions

#### 1. Get Razorpay Credentials

1. Sign up at [Razorpay Dashboard](https://dashboard.razorpay.com/)
2. Go to Settings → API Keys
3. Generate API Keys for your account
4. Copy the Key ID and Key Secret

#### 2. Update Environment Variables

Update your `.env` file with your Razorpay credentials:

```env
# Razorpay Configuration
RAZORPAY_KEY_ID="rzp_test_your_key_id_here"
RAZORPAY_KEY_SECRET="your_key_secret_here"
```

**Important:** 
- Use `rzp_test_` prefix for test mode
- Use `rzp_live_` prefix for production mode
- Never commit your actual credentials to version control

#### 3. Test the Payment Flow

1. **Create an Advertiser Account**
   - Sign up as an advertiser
   - Complete the onboarding process

2. **Try Creating a Campaign**
   - Go to Create Campaign
   - Set a budget higher than your current balance (₹0)
   - You should see an "Insufficient balance" error with "Add Funds" button

3. **Add Funds to Wallet**
   - Click "Add Funds" or go to Billing page
   - Enter an amount (minimum ₹100)
   - Complete the Razorpay payment flow
   - Verify balance is updated

4. **Create Campaign Successfully**
   - Return to campaign creation
   - Your new balance should allow campaign creation

### API Endpoints

#### Payment Endpoints
- `POST /api/advertiser/payments/create-order` - Create Razorpay order
- `POST /api/advertiser/payments/verify` - Verify payment and update balance

#### Wallet Endpoints
- `GET /api/advertiser/wallet` - Get wallet data and transaction history

#### Dashboard Integration
- `GET /api/advertiser/dashboard` - Includes current balance as `remainingBudget`

### Database Schema

The following tables were added to support payments:

```prisma
model PaymentOrder {
  id               String   @id @default(auto()) @map("_id") @db.ObjectId
  advertiserId     String   @db.ObjectId
  amount           Float
  currency         String   @default("INR")
  razorpayOrderId  String   @unique
  status           String   @default("CREATED")
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
}

model PaymentTransaction {
  id                  String   @id @default(auto()) @map("_id") @db.ObjectId
  advertiserId        String   @db.ObjectId
  orderId             String   @db.ObjectId
  razorpayPaymentId   String?
  razorpaySignature   String?
  amount              Float
  currency            String   @default("INR")
  status              String   @default("PENDING")
  failureReason       String?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
}

model WalletTransaction {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  advertiserId      String   @db.ObjectId
  type              String   // CREDIT, DEBIT
  amount            Float
  description       String
  balanceAfter      Float
  relatedOrderId    String?  @db.ObjectId
  relatedCampaignId String?  @db.ObjectId
  createdAt         DateTime @default(now())
}
```

### Security Features

1. **Payment Verification**
   - Razorpay signature verification
   - Server-side payment validation
   - Secure webhook handling

2. **Balance Protection**
   - Atomic transactions for balance updates
   - Insufficient balance checks before campaign creation
   - Transaction logging for audit trail

3. **Error Handling**
   - Graceful payment failure handling
   - User-friendly error messages
   - Automatic retry mechanisms

### Testing with Razorpay Test Mode

Razorpay provides test cards for testing:

**Test Card Numbers:**
- Success: 4111 1111 1111 1111
- Failure: 4000 0000 0000 0002

**Test Details:**
- CVV: Any 3 digits
- Expiry: Any future date
- Name: Any name

### Production Considerations

1. **Switch to Live Mode**
   - Update credentials to live keys
   - Test thoroughly in staging environment
   - Set up webhooks for production

2. **Monitoring**
   - Set up payment failure alerts
   - Monitor transaction success rates
   - Track wallet balance changes

3. **Compliance**
   - Ensure PCI compliance
   - Implement proper logging
   - Set up audit trails

### Troubleshooting

**Common Issues:**

1. **"Razorpay is not defined" Error**
   - Ensure Razorpay script is loaded
   - Check network connectivity
   - Verify script URL

2. **Payment Verification Failed**
   - Check Razorpay webhook secret
   - Verify signature calculation
   - Check server logs for errors

3. **Balance Not Updated**
   - Check payment transaction status
   - Verify database connection
   - Check for transaction rollbacks

### Support

For issues with the payment system:
1. Check browser console for JavaScript errors
2. Check server logs for API errors
3. Verify Razorpay dashboard for payment status
4. Contact Razorpay support for payment gateway issues
