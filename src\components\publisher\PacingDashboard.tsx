"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { toast } from "sonner"
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  DollarSign, 
  BarChart3, 
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  MousePointer,
  Star
} from "lucide-react"

interface PublisherPacingInsights {
  pacingSettings: any
  overallMetrics: any
  adSpaceInsights: any[]
  revenueProjection: any
  optimizationTips: string[]
}

export default function PublisherPacingDashboard() {
  const [insights, setInsights] = useState<PublisherPacingInsights | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [settings, setSettings] = useState({
    dailyRevenuePacing: "EVEN",
    adServingStrategy: "REVENUE_OPTIMIZED",
    minFillRate: 0.8,
    maxAdFrequency: 3,
    minAdQualityScore: 0.7,
    blockLowPerformingAds: true,
    dailyRevenueTarget: "",
    weeklyRevenueTarget: "",
    monthlyRevenueTarget: ""
  })

  useEffect(() => {
    fetchPacingInsights()
  }, [])

  const fetchPacingInsights = async () => {
    try {
      const response = await fetch("/api/publisher/pacing")
      if (response.ok) {
        const data = await response.json()
        setInsights(data.data)
        if (data.data?.pacingSettings) {
          setSettings({
            dailyRevenuePacing: data.data.pacingSettings.dailyRevenuePacing || "EVEN",
            adServingStrategy: data.data.pacingSettings.adServingStrategy || "REVENUE_OPTIMIZED",
            minFillRate: data.data.pacingSettings.minFillRate || 0.8,
            maxAdFrequency: data.data.pacingSettings.maxAdFrequency || 3,
            minAdQualityScore: data.data.pacingSettings.minAdQualityScore || 0.7,
            blockLowPerformingAds: data.data.pacingSettings.blockLowPerformingAds ?? true,
            dailyRevenueTarget: data.data.pacingSettings.dailyRevenueTarget?.toString() || "",
            weeklyRevenueTarget: data.data.pacingSettings.weeklyRevenueTarget?.toString() || "",
            monthlyRevenueTarget: data.data.pacingSettings.monthlyRevenueTarget?.toString() || ""
          })
        }
      } else {
        toast.error("Failed to fetch pacing insights")
      }
    } catch (error) {
      console.error("Error fetching pacing insights:", error)
      toast.error("Error loading pacing data")
    } finally {
      setLoading(false)
    }
  }

  const updatePacingSettings = async () => {
    setUpdating(true)
    try {
      const payload = {
        ...settings,
        dailyRevenueTarget: settings.dailyRevenueTarget ? parseFloat(settings.dailyRevenueTarget) : null,
        weeklyRevenueTarget: settings.weeklyRevenueTarget ? parseFloat(settings.weeklyRevenueTarget) : null,
        monthlyRevenueTarget: settings.monthlyRevenueTarget ? parseFloat(settings.monthlyRevenueTarget) : null
      }

      const response = await fetch("/api/publisher/pacing", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload)
      })

      if (response.ok) {
        toast.success("Pacing settings updated successfully!")
        fetchPacingInsights()
      } else {
        const error = await response.json()
        toast.error(error.message || "Failed to update settings")
      }
    } catch (error) {
      console.error("Error updating pacing settings:", error)
      toast.error("Error updating settings")
    } finally {
      setUpdating(false)
    }
  }

  const triggerOptimization = async () => {
    try {
      const response = await fetch("/api/publisher/pacing/optimize", {
        method: "POST"
      })

      if (response.ok) {
        toast.success("Optimization triggered successfully!")
        fetchPacingInsights()
      } else {
        toast.error("Failed to trigger optimization")
      }
    } catch (error) {
      console.error("Error triggering optimization:", error)
      toast.error("Error triggering optimization")
    }
  }

  const getAdSpaceStatusColor = (status: string) => {
    switch (status) {
      case "HIGH_PERFORMING": return "bg-green-500"
      case "EXCELLENT_CTR": return "bg-blue-500"
      case "NEEDS_ATTENTION": return "bg-red-500"
      case "LOW_FILL_RATE": return "bg-yellow-500"
      default: return "bg-gray-500"
    }
  }

  const getAdSpaceStatusIcon = (status: string) => {
    switch (status) {
      case "HIGH_PERFORMING": return <TrendingUp className="h-4 w-4" />
      case "EXCELLENT_CTR": return <Target className="h-4 w-4" />
      case "NEEDS_ATTENTION": return <AlertTriangle className="h-4 w-4" />
      case "LOW_FILL_RATE": return <Eye className="h-4 w-4" />
      default: return <BarChart3 className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Revenue Pacing Dashboard</h1>
          <p className="text-muted-foreground">
            Optimize your ad space performance and revenue with intelligent pacing
          </p>
        </div>
        <Button onClick={triggerOptimization} variant="outline">
          <TrendingUp className="h-4 w-4 mr-2" />
          Optimize Now
        </Button>
      </div>

      {/* Overall Performance Cards */}
      {insights?.overallMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ₹{insights.overallMetrics.revenue.toFixed(2)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">RPM</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ₹{insights.overallMetrics.rpm.toFixed(2)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Fill Rate</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {(insights.overallMetrics.fillRate * 100).toFixed(1)}%
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">CTR</CardTitle>
              <MousePointer className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {insights.overallMetrics.ctr.toFixed(2)}%
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Quality Score</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {(insights.overallMetrics.qualityScore * 100).toFixed(0)}%
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Revenue Projection */}
      {insights?.revenueProjection && (
        <Card>
          <CardHeader>
            <CardTitle>Revenue Projection</CardTitle>
            <CardDescription>Based on current performance trends</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label className="text-sm text-muted-foreground">Current Daily</Label>
                <div className="text-xl font-bold">₹{insights.revenueProjection.currentDaily.toFixed(2)}</div>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">Projected Monthly</Label>
                <div className="text-xl font-bold">₹{insights.revenueProjection.projectedMonthly.toFixed(2)}</div>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">Target Monthly</Label>
                <div className="text-xl font-bold">₹{insights.revenueProjection.targetMonthly.toFixed(2)}</div>
                {insights.revenueProjection.growthNeeded > 0 && (
                  <p className="text-xs text-muted-foreground">
                    {insights.revenueProjection.growthNeeded.toFixed(1)}% growth needed
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="adspaces" className="space-y-4">
        <TabsList>
          <TabsTrigger value="adspaces">Ad Space Performance</TabsTrigger>
          <TabsTrigger value="settings">Pacing Settings</TabsTrigger>
          <TabsTrigger value="tips">Optimization Tips</TabsTrigger>
        </TabsList>

        <TabsContent value="adspaces" className="space-y-4">
          {insights?.adSpaceInsights?.map((adSpace) => (
            <Card key={adSpace.adSpace.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">{adSpace.adSpace.name}</CardTitle>
                    <CardDescription>
                      {adSpace.adSpace.format} • {adSpace.adSpace.width}x{adSpace.adSpace.height} • {adSpace.adSpace.position}
                    </CardDescription>
                  </div>
                  <Badge 
                    className={`${getAdSpaceStatusColor(adSpace.status)} text-white`}
                  >
                    {getAdSpaceStatusIcon(adSpace.status)}
                    <span className="ml-1">{adSpace.status.replace('_', ' ')}</span>
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <Label className="text-sm text-muted-foreground">Revenue</Label>
                    <div className="text-lg font-semibold">₹{adSpace.metrics.revenue.toFixed(2)}</div>
                  </div>
                  <div>
                    <Label className="text-sm text-muted-foreground">RPM</Label>
                    <div className="text-lg font-semibold">₹{adSpace.metrics.rpm.toFixed(2)}</div>
                  </div>
                  <div>
                    <Label className="text-sm text-muted-foreground">Fill Rate</Label>
                    <div className="text-lg font-semibold">{(adSpace.metrics.fillRate * 100).toFixed(1)}%</div>
                  </div>
                  <div>
                    <Label className="text-sm text-muted-foreground">CTR</Label>
                    <div className="text-lg font-semibold">{adSpace.metrics.ctr.toFixed(2)}%</div>
                  </div>
                </div>

                {adSpace.recommendations.length > 0 && (
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <h4 className="font-medium text-sm mb-2">Recommendations</h4>
                    <ul className="space-y-1">
                      {adSpace.recommendations.map((rec: string, index: number) => (
                        <li key={index} className="text-sm text-muted-foreground">
                          • {rec}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                Revenue Pacing Configuration
              </CardTitle>
              <CardDescription>
                Configure how your ad spaces should optimize for revenue and performance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="dailyRevenuePacing">Daily Revenue Pacing</Label>
                    <Select 
                      value={settings.dailyRevenuePacing} 
                      onValueChange={(value) => setSettings({...settings, dailyRevenuePacing: value})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="EVEN">Even Distribution</SelectItem>
                        <SelectItem value="FRONT_LOADED">Front Loaded</SelectItem>
                        <SelectItem value="BACK_LOADED">Back Loaded</SelectItem>
                        <SelectItem value="AGGRESSIVE">Aggressive</SelectItem>
                        <SelectItem value="CONSERVATIVE">Conservative</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="adServingStrategy">Ad Serving Strategy</Label>
                    <Select 
                      value={settings.adServingStrategy} 
                      onValueChange={(value) => setSettings({...settings, adServingStrategy: value})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="REVENUE_OPTIMIZED">Revenue Optimized</SelectItem>
                        <SelectItem value="FILL_RATE_OPTIMIZED">Fill Rate Optimized</SelectItem>
                        <SelectItem value="QUALITY_OPTIMIZED">Quality Optimized</SelectItem>
                        <SelectItem value="BALANCED">Balanced</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="minFillRate">Minimum Fill Rate</Label>
                    <Input
                      id="minFillRate"
                      type="number"
                      step="0.01"
                      min="0"
                      max="1"
                      value={settings.minFillRate}
                      onChange={(e) => setSettings({...settings, minFillRate: parseFloat(e.target.value) || 0})}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Minimum acceptable fill rate (0-1)
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="minAdQualityScore">Minimum Ad Quality Score</Label>
                    <Input
                      id="minAdQualityScore"
                      type="number"
                      step="0.01"
                      min="0"
                      max="1"
                      value={settings.minAdQualityScore}
                      onChange={(e) => setSettings({...settings, minAdQualityScore: parseFloat(e.target.value) || 0})}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Minimum quality score for ads (0-1)
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="dailyRevenueTarget">Daily Revenue Target (₹)</Label>
                  <Input
                    id="dailyRevenueTarget"
                    type="number"
                    step="0.01"
                    placeholder="e.g., 100.00"
                    value={settings.dailyRevenueTarget}
                    onChange={(e) => setSettings({...settings, dailyRevenueTarget: e.target.value})}
                  />
                </div>

                <div>
                  <Label htmlFor="weeklyRevenueTarget">Weekly Revenue Target (₹)</Label>
                  <Input
                    id="weeklyRevenueTarget"
                    type="number"
                    step="0.01"
                    placeholder="e.g., 700.00"
                    value={settings.weeklyRevenueTarget}
                    onChange={(e) => setSettings({...settings, weeklyRevenueTarget: e.target.value})}
                  />
                </div>

                <div>
                  <Label htmlFor="monthlyRevenueTarget">Monthly Revenue Target (₹)</Label>
                  <Input
                    id="monthlyRevenueTarget"
                    type="number"
                    step="0.01"
                    placeholder="e.g., 3000.00"
                    value={settings.monthlyRevenueTarget}
                    onChange={(e) => setSettings({...settings, monthlyRevenueTarget: e.target.value})}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="blockLowPerformingAds"
                  checked={settings.blockLowPerformingAds}
                  onCheckedChange={(checked) => setSettings({...settings, blockLowPerformingAds: checked})}
                />
                <Label htmlFor="blockLowPerformingAds">
                  Automatically block low-performing ads
                </Label>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={fetchPacingInsights}>
                  Reset
                </Button>
                <Button onClick={updatePacingSettings} disabled={updating}>
                  {updating ? "Updating..." : "Save Settings"}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tips" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Optimization Tips</CardTitle>
              <CardDescription>
                AI-powered recommendations to improve your revenue
              </CardDescription>
            </CardHeader>
            <CardContent>
              {insights?.optimizationTips?.length ? (
                <ul className="space-y-3">
                  {insights.optimizationTips.map((tip, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{tip}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-muted-foreground">
                  No optimization tips available at the moment. Keep monitoring your performance!
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
