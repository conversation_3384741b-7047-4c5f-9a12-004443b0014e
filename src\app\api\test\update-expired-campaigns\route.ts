import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// GET /api/test/update-expired-campaigns - Test endpoint to manually update expired campaigns
export async function GET(request: NextRequest) {
  try {
    const now = new Date()
    
    // Find active campaigns that have passed their end date
    const expiredCampaigns = await prisma.campaign.findMany({
      where: {
        status: "ACTIVE",
        endDate: {
          lt: now
        }
      },
      select: {
        id: true,
        name: true,
        endDate: true,
        status: true,
        advertiser: {
          select: {
            id: true,
            companyName: true,
            balance: true
          }
        }
      }
    })

    console.log(`Found ${expiredCampaigns.length} expired campaigns`)

    const updatedCampaigns = []

    // Update expired campaigns to PAUSED status (so they can be reactivated if end date is extended)
    for (const campaign of expiredCampaigns) {
      const updatedCampaign = await prisma.campaign.update({
        where: { id: campaign.id },
        data: {
          status: "PAUSED",
          updatedAt: now
        },
        select: {
          id: true,
          name: true,
          status: true,
          endDate: true,
          updatedAt: true
        }
      })

      // Log the pause in wallet transactions for audit trail
      await prisma.walletTransaction.create({
        data: {
          advertiserId: campaign.advertiser.id,
          type: "CREDIT", // No actual credit, just logging
          amount: 0,
          description: `Campaign "${campaign.name}" automatically paused (end date reached)`,
          balanceAfter: campaign.advertiser.balance,
          relatedCampaignId: campaign.id,
        },
      })

      updatedCampaigns.push(updatedCampaign)
      console.log(`Updated campaign ${campaign.id} (${campaign.name}) from ACTIVE to PAUSED`)
    }

    return NextResponse.json({
      success: true,
      message: `Updated ${updatedCampaigns.length} expired campaigns to PAUSED status`,
      expiredCampaigns: expiredCampaigns.map(c => ({
        id: c.id,
        name: c.name,
        endDate: c.endDate,
        previousStatus: "ACTIVE",
        newStatus: "PAUSED"
      })),
      updatedCampaigns,
      totalUpdated: updatedCampaigns.length,
      currentTime: now
    })

  } catch (error) {
    console.error("Error updating expired campaigns:", error)
    
    return NextResponse.json(
      { 
        success: false,
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    )
  }
}

// POST /api/test/update-expired-campaigns - Same as GET but with POST method
export async function POST(request: NextRequest) {
  return GET(request)
}
