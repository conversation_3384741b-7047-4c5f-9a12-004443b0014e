import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { subDays, format, startOfDay, endOfDay } from "date-fns"

// GET /api/advertiser/analytics - Get analytics data for advertiser
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get URL parameters
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get("days") || "30")
    const campaignId = searchParams.get("campaignId")

    // Get advertiser profile
    const advertiserProfile = await prisma.advertiserProfile.findUnique({
      where: { userId: session.user.id },
    })

    if (!advertiserProfile) {
      return NextResponse.json(
        { message: "Advertiser profile not found" },
        { status: 404 }
      )
    }

    // Build date range
    const endDate = new Date()
    const startDate = subDays(endDate, days)

    // Build where clause for campaigns
    const campaignWhere: any = {
      advertiserId: advertiserProfile.id,
    }

    if (campaignId) {
      campaignWhere.id = campaignId
    }

    // Get campaigns with placements data
    const campaigns = await prisma.campaign.findMany({
      where: campaignWhere,
      include: {
        adPlacements: {
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate,
            },
          },
          select: {
            impressions: true,
            clicks: true,
            conversions: true,
            cost: true,
            createdAt: true,
          },
        },
      },
    })

    // Generate daily analytics data
    const analyticsData = []
    for (let i = 0; i < days; i++) {
      const currentDate = subDays(endDate, i)
      const dayStart = startOfDay(currentDate)
      const dayEnd = endOfDay(currentDate)

      let daySpent = 0
      let dayImpressions = 0
      let dayClicks = 0
      let dayConversions = 0

      campaigns.forEach((campaign) => {
        campaign.adPlacements.forEach((placement) => {
          const placementDate = new Date(placement.createdAt)
          if (placementDate >= dayStart && placementDate <= dayEnd) {
            daySpent += placement.cost
            dayImpressions += placement.impressions
            dayClicks += placement.clicks
            dayConversions += placement.conversions
          }
        })
      })

      const dayCtr = dayImpressions > 0 ? (dayClicks / dayImpressions) * 100 : 0
      const dayCostPerClick = dayClicks > 0 ? daySpent / dayClicks : 0
      const dayCostPerConversion = dayConversions > 0 ? daySpent / dayConversions : 0

      analyticsData.unshift({
        date: format(currentDate, "MMM dd"),
        spent: parseFloat(daySpent.toFixed(2)),
        impressions: dayImpressions,
        clicks: dayClicks,
        conversions: dayConversions,
        ctr: parseFloat(dayCtr.toFixed(2)),
        costPerClick: parseFloat(dayCostPerClick.toFixed(2)),
        costPerConversion: parseFloat(dayCostPerConversion.toFixed(2)),
      })
    }

    // Calculate campaign performance data
    const campaignPerformance = campaigns.map((campaign) => {
      const totalImpressions = campaign.adPlacements.reduce((sum, p) => sum + p.impressions, 0)
      const totalClicks = campaign.adPlacements.reduce((sum, p) => sum + p.clicks, 0)
      const totalConversions = campaign.adPlacements.reduce((sum, p) => sum + p.conversions, 0)
      const totalSpent = campaign.adPlacements.reduce((sum, p) => sum + p.cost, 0)
      const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0

      return {
        name: campaign.name,
        spent: parseFloat(totalSpent.toFixed(2)),
        impressions: totalImpressions,
        clicks: totalClicks,
        conversions: totalConversions,
        ctr: parseFloat(ctr.toFixed(2)),
      }
    }).filter(campaign => campaign.spent > 0) // Only include campaigns with spending

    // Calculate regional data (mock data for now - would need geo tracking)
    const regionData = [
      {
        region: "North America",
        spent: campaignPerformance.reduce((sum, c) => sum + c.spent, 0) * 0.4,
        conversions: campaignPerformance.reduce((sum, c) => sum + c.conversions, 0) * 0.35,
        fill: "hsl(var(--chart-1))",
      },
      {
        region: "Europe",
        spent: campaignPerformance.reduce((sum, c) => sum + c.spent, 0) * 0.3,
        conversions: campaignPerformance.reduce((sum, c) => sum + c.conversions, 0) * 0.25,
        fill: "hsl(var(--chart-2))",
      },
      {
        region: "Asia",
        spent: campaignPerformance.reduce((sum, c) => sum + c.spent, 0) * 0.2,
        conversions: campaignPerformance.reduce((sum, c) => sum + c.conversions, 0) * 0.3,
        fill: "hsl(var(--chart-3))",
      },
      {
        region: "Other",
        spent: campaignPerformance.reduce((sum, c) => sum + c.spent, 0) * 0.1,
        conversions: campaignPerformance.reduce((sum, c) => sum + c.conversions, 0) * 0.1,
        fill: "hsl(var(--chart-4))",
      },
    ].map(region => ({
      ...region,
      spent: parseFloat(region.spent.toFixed(2)),
    }))

    // Calculate overall metrics
    const totalSpent = analyticsData.reduce((sum, day) => sum + day.spent, 0)
    const totalImpressions = analyticsData.reduce((sum, day) => sum + day.impressions, 0)
    const totalClicks = analyticsData.reduce((sum, day) => sum + day.clicks, 0)
    const totalConversions = analyticsData.reduce((sum, day) => sum + day.conversions, 0)
    const overallCtr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0
    const overallCostPerClick = totalClicks > 0 ? totalSpent / totalClicks : 0
    const overallCostPerConversion = totalConversions > 0 ? totalSpent / totalConversions : 0

    // Calculate trends (compare with previous period)
    const previousPeriodStart = subDays(startDate, days)
    const previousPeriodEnd = startDate

    const previousPlacements = await prisma.adPlacement.findMany({
      where: {
        campaign: {
          advertiserId: advertiserProfile.id,
        },
        createdAt: {
          gte: previousPeriodStart,
          lt: previousPeriodEnd,
        },
      },
      select: {
        impressions: true,
        clicks: true,
        conversions: true,
        cost: true,
      },
    })

    const previousSpent = previousPlacements.reduce((sum, p) => sum + p.cost, 0)
    const previousImpressions = previousPlacements.reduce((sum, p) => sum + p.impressions, 0)
    const previousClicks = previousPlacements.reduce((sum, p) => sum + p.clicks, 0)
    const previousConversions = previousPlacements.reduce((sum, p) => sum + p.conversions, 0)

    const spentTrend = previousSpent > 0 ? ((totalSpent - previousSpent) / previousSpent) * 100 : 0
    const impressionsTrend = previousImpressions > 0 ? ((totalImpressions - previousImpressions) / previousImpressions) * 100 : 0
    const clicksTrend = previousClicks > 0 ? ((totalClicks - previousClicks) / previousClicks) * 100 : 0
    const conversionsTrend = previousConversions > 0 ? ((totalConversions - previousConversions) / previousConversions) * 100 : 0

    return NextResponse.json({
      analyticsData,
      campaignPerformance,
      regionData,
      summary: {
        totalSpent: parseFloat(totalSpent.toFixed(2)),
        totalImpressions,
        totalClicks,
        totalConversions,
        overallCtr: parseFloat(overallCtr.toFixed(2)),
        overallCostPerClick: parseFloat(overallCostPerClick.toFixed(2)),
        overallCostPerConversion: parseFloat(overallCostPerConversion.toFixed(2)),
        trends: {
          spent: parseFloat(spentTrend.toFixed(2)),
          impressions: parseFloat(impressionsTrend.toFixed(2)),
          clicks: parseFloat(clicksTrend.toFixed(2)),
          conversions: parseFloat(conversionsTrend.toFixed(2)),
        },
      },
      dateRange: {
        start: format(startDate, "yyyy-MM-dd"),
        end: format(endDate, "yyyy-MM-dd"),
        days,
      },
    })
  } catch (error) {
    console.error("Analytics error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
