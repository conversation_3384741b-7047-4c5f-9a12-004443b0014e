<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ad Network - Test Ad Serving</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .ad-container {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            background-color: #fafafa;
            border-radius: 4px;
        }

        .ad-container.loaded {
            border-color: #4CAF50;
            background-color: #f0f8f0;
        }

        .ad-container.error {
            border-color: #f44336;
            background-color: #fff0f0;
        }

        .controls {
            margin: 20px 0;
            padding: 15px;
            background-color: #e3f2fd;
            border-radius: 4px;
        }

        .controls button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }

        .controls button:hover {
            background-color: #1976D2;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: #fff;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #2196F3;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2196F3;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .log {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }

        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }

        .log-entry.success {
            color: #4CAF50;
        }

        .log-entry.error {
            color: #f44336;
        }

        .log-entry.info {
            color: #2196F3;
        }
    </style>
    <script src="https://localhost:3000/sdk.js"></script>
</head>

<body>
    <div class="container">
        <h1>Ad Network - Real-Time Ad Serving Test</h1>
        <p>This page demonstrates the real-time ad serving functionality of the Ad Network platform.</p>

        <div class="controls">
            <h3>Test Controls</h3>
            <button onclick="loadAds()">Load Ads</button>
            <button onclick="refreshAds()">Refresh All Ads</button>
            <button onclick="clearLog()">Clear Log</button>
            <button onclick="simulateClicks()">Simulate Clicks</button>
            <button onclick="simulateConversions()">Simulate Conversions</button>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-value" id="impressions">0</div>
                <div class="stat-label">Impressions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="clicks">0</div>
                <div class="stat-label">Clicks</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="conversions">0</div>
                <div class="stat-label">Conversions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="revenue">$0.00</div>
                <div class="stat-label">Revenue</div>
            </div>
        </div>

        <h3>Ad Placements</h3>

        <!-- Banner Ad (728x90) -->
        <div class="ad-container" id="banner-ad">
            <h4>Banner Ad (728x90)</h4>
            <div id="ad-banner" data-adnetwork-auto data-api-key="test-api-key" data-ad-space-id="test-banner-space"
                data-format="BANNER" data-width="728" data-height="90">
                Loading banner ad...
            </div>
        </div>

        <!-- Rectangle Ad (300x250) -->
        <div class="ad-container" id="rectangle-ad">
            <h4>Rectangle Ad (300x250)</h4>
            <div id="ad-rectangle" data-adnetwork-auto data-api-key="test-api-key"
                data-ad-space-id="test-rectangle-space" data-format="BANNER" data-width="300" data-height="250">
                Loading rectangle ad...
            </div>
        </div>

        <!-- Sidebar Ad (160x600) -->
        <div class="ad-container" id="sidebar-ad">
            <h4>Sidebar Ad (160x600)</h4>
            <div id="ad-sidebar" data-adnetwork-auto data-api-key="test-api-key" data-ad-space-id="test-sidebar-space"
                data-format="BANNER" data-width="160" data-height="600">
                Loading sidebar ad...
            </div>
        </div>

        <h3>Activity Log</h3>
        <div class="log" id="activity-log">
            <div class="log-entry info">Ad Network Test Page Loaded</div>
        </div>
    </div>

    <!-- Load Ad Network SDK -->
    <script src="/sdk/adnetwork-sdk.js"></script>

    <script>
        let stats = {
            impressions: 0,
            clicks: 0,
            conversions: 0,
            revenue: 0
        };

        function log(message, type = 'info') {
            const logElement = document.getElementById('activity-log');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStats() {
            document.getElementById('impressions').textContent = stats.impressions;
            document.getElementById('clicks').textContent = stats.clicks;
            document.getElementById('conversions').textContent = stats.conversions;
            document.getElementById('revenue').textContent = `$${stats.revenue.toFixed(2)}`;
        }

        function loadAds() {
            log('Loading ads...', 'info');

            // Initialize SDK
            if (window.AdNetwork) {
                window.AdNetwork.init({
                    apiKey: 'test-api-key',
                    debug: true,
                    autoRefresh: false
                });

                // Load each ad space
                const adSpaces = ['ad-banner', 'ad-rectangle', 'ad-sidebar'];
                adSpaces.forEach(adSpaceId => {
                    const element = document.getElementById(adSpaceId);
                    if (element) {
                        window.AdNetwork.loadAd(element, {
                            onLoad: (ad) => {
                                log(`Ad loaded in ${adSpaceId}: ${ad.title}`, 'success');
                                stats.impressions++;
                                updateStats();

                                // Mark container as loaded
                                const container = element.closest('.ad-container');
                                if (container) {
                                    container.classList.add('loaded');
                                }
                            },
                            onError: (error) => {
                                log(`Failed to load ad in ${adSpaceId}: ${error}`, 'error');

                                // Mark container as error
                                const container = element.closest('.ad-container');
                                if (container) {
                                    container.classList.add('error');
                                }
                            },
                            onClick: () => {
                                log(`Ad clicked in ${adSpaceId}`, 'success');
                                stats.clicks++;
                                stats.revenue += 0.50; // Simulate CPC revenue
                                updateStats();
                            }
                        });
                    }
                });
            } else {
                log('AdNetwork SDK not loaded', 'error');
            }
        }

        function refreshAds() {
            log('Refreshing all ads...', 'info');
            if (window.AdNetwork) {
                window.AdNetwork.refreshAll();
            }
        }

        function clearLog() {
            const logElement = document.getElementById('activity-log');
            logElement.innerHTML = '<div class="log-entry info">Log cleared</div>';
        }

        function simulateClicks() {
            log('Simulating ad clicks...', 'info');
            stats.clicks += Math.floor(Math.random() * 5) + 1;
            stats.revenue += (Math.random() * 2) + 0.25;
            updateStats();
        }

        function simulateConversions() {
            log('Simulating conversions...', 'info');
            stats.conversions += Math.floor(Math.random() * 3) + 1;
            stats.revenue += (Math.random() * 10) + 2;
            updateStats();
        }

        // Auto-load ads when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('Auto-loading ads...', 'info');
                loadAds();
            }, 1000);
        });

        // Update stats periodically
        setInterval(updateStats, 1000);
    </script>

    <script>
        // Initialize the Ad Network SDK
        AdNetwork.init({
            apiKey: 'ak_live_1234567890abcdef1234567890abcdef12345678',
            container: 'ad-container',
            // Optional configuration
            autoRefresh: true,
            refreshInterval: 30000, // 30 seconds
            onAdLoaded: function (ad) {
                console.log('Ad loaded:', ad);
            },
            onAdClicked: function (ad) {
                console.log('Ad clicked:', ad);
            }
        });
    </script>
</body>

</html>