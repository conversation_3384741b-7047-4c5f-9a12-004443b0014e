import { NextAuthOptions } from "next-auth"
import Cred<PERSON><PERSON><PERSON><PERSON><PERSON> from "next-auth/providers/credentials"
import { PrismaAdapter } from "@next-auth/prisma-adapter"
import { prisma } from "@/lib/prisma"
import bcrypt from "bcryptjs"
import { UserRole } from "@prisma/client"

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email,
          },
          include: {
            publisherProfile: true,
            advertiserProfile: true,
          },
        })

        if (!user || !user.password) {
          return null
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        )

        if (!isPasswordValid) {
          return null
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role || undefined,
          isOnboarded: user.isOnboarded,
          image: user.image,
        } as any
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      // If this is a sign-in, update token with user data
      if (user) {
        token.role = user.role
        token.isOnboarded = user.isOnboarded
      }

      // If this is a session update (e.g., after onboarding), refresh from database
      if (trigger === "update" && token.sub) {
        const dbUser = await prisma.user.findUnique({
          where: { id: token.sub },
          select: {
            role: true,
            isOnboarded: true,
          },
        })

        if (dbUser) {
          token.role = dbUser.role || undefined
          token.isOnboarded = dbUser.isOnboarded
        }
      }

      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as UserRole
        session.user.isOnboarded = token.isOnboarded as boolean
      }
      return session
    },
  },
  pages: {
    signIn: "/auth/signin",
    // signUp: "/auth/signup", // Custom page not supported in NextAuth
  },
  secret: process.env.NEXTAUTH_SECRET,
}

declare module "next-auth" {
  interface User {
    role?: UserRole
    isOnboarded?: boolean
  }

  interface Session {
    user: {
      id: string
      email: string
      name?: string
      image?: string
      role?: UserRole
      isOnboarded?: boolean
    }
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role?: UserRole
    isOnboarded?: boolean
  }
}
