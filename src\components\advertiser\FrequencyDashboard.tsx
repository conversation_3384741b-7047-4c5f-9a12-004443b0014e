"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "sonner"
import { 
  Eye, 
  Users, 
  BarChart3, 
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  Smartphone,
  Monitor,
  Tablet
} from "lucide-react"

interface FrequencyAnalytics {
  campaigns: Array<{
    campaign: {
      id: string
      name: string
      frequencyCap: number
      frequencyPeriod: string
      status: string
    }
    analytics: {
      totalUsers: number
      averageFrequency: number
      frequencyDistribution: Array<{ frequency: number; users: number }>
      cappedUsers: number
      topFrequencyUsers: Array<{ fingerprint: string; impressions: number }>
    }
  }>
  summary: {
    totalCampaigns: number
    activeCampaigns: number
    totalUsers: number
    averageFrequency: number
  }
}

export default function FrequencyDashboard() {
  const [analytics, setAnalytics] = useState<FrequencyAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [selectedCampaign, setSelectedCampaign] = useState<string>("")
  const [frequencySettings, setFrequencySettings] = useState({
    frequencyCap: 5,
    frequencyPeriod: "DAILY"
  })

  useEffect(() => {
    fetchFrequencyAnalytics()
  }, [])

  const fetchFrequencyAnalytics = async () => {
    try {
      const response = await fetch("/api/advertiser/frequency")
      if (response.ok) {
        const data = await response.json()
        setAnalytics(data.data)
        
        // Set default selected campaign
        if (data.data.campaigns.length > 0 && !selectedCampaign) {
          const firstCampaign = data.data.campaigns[0].campaign
          setSelectedCampaign(firstCampaign.id)
          setFrequencySettings({
            frequencyCap: firstCampaign.frequencyCap,
            frequencyPeriod: firstCampaign.frequencyPeriod
          })
        }
      } else {
        toast.error("Failed to fetch frequency analytics")
      }
    } catch (error) {
      console.error("Error fetching frequency analytics:", error)
      toast.error("Error loading frequency data")
    } finally {
      setLoading(false)
    }
  }

  const updateFrequencySettings = async () => {
    if (!selectedCampaign) {
      toast.error("Please select a campaign")
      return
    }

    setUpdating(true)
    try {
      const response = await fetch("/api/advertiser/frequency", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          campaignId: selectedCampaign,
          ...frequencySettings
        })
      })

      if (response.ok) {
        toast.success("Frequency settings updated successfully!")
        fetchFrequencyAnalytics()
      } else {
        const error = await response.json()
        toast.error(error.message || "Failed to update settings")
      }
    } catch (error) {
      console.error("Error updating frequency settings:", error)
      toast.error("Error updating settings")
    } finally {
      setUpdating(false)
    }
  }

  const getFrequencyColor = (frequency: number, cap: number) => {
    const ratio = frequency / cap
    if (ratio >= 1) return "text-red-600"
    if (ratio >= 0.8) return "text-yellow-600"
    return "text-green-600"
  }

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case "mobile": return <Smartphone className="h-4 w-4" />
      case "tablet": return <Tablet className="h-4 w-4" />
      default: return <Monitor className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  const selectedCampaignData = analytics?.campaigns.find(c => c.campaign.id === selectedCampaign)

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Frequency Management</h1>
          <p className="text-muted-foreground">
            Control how often your ads are shown to the same users
          </p>
        </div>
        <Button onClick={fetchFrequencyAnalytics} variant="outline">
          <BarChart3 className="h-4 w-4 mr-2" />
          Refresh Data
        </Button>
      </div>

      {/* Summary Cards */}
      {analytics?.summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.summary.totalCampaigns}</div>
              <p className="text-xs text-muted-foreground">
                {analytics.summary.activeCampaigns} active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.summary.totalUsers.toLocaleString()}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Frequency</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.summary.averageFrequency.toFixed(1)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Frequency Health</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {analytics.summary.averageFrequency <= 5 ? "Good" : "High"}
              </div>
              <p className="text-xs text-muted-foreground">
                {analytics.summary.averageFrequency <= 3 ? "Optimal range" : 
                 analytics.summary.averageFrequency <= 5 ? "Acceptable" : "Consider reducing"}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Campaign Overview</TabsTrigger>
          <TabsTrigger value="settings">Frequency Settings</TabsTrigger>
          <TabsTrigger value="analytics">Detailed Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {analytics?.campaigns.map(({ campaign, analytics: campAnalytics }) => (
            <Card key={campaign.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">{campaign.name}</CardTitle>
                    <CardDescription>
                      Frequency Cap: {campaign.frequencyCap} per {campaign.frequencyPeriod.toLowerCase()}
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={campaign.status === "ACTIVE" ? "default" : "secondary"}>
                      {campaign.status}
                    </Badge>
                    {campAnalytics.cappedUsers > 0 && (
                      <Badge variant="destructive">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        {campAnalytics.cappedUsers} Capped
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <Label className="text-sm text-muted-foreground">Total Users</Label>
                    <div className="text-xl font-semibold">{campAnalytics.totalUsers.toLocaleString()}</div>
                  </div>
                  <div>
                    <Label className="text-sm text-muted-foreground">Avg Frequency</Label>
                    <div className={`text-xl font-semibold ${getFrequencyColor(campAnalytics.averageFrequency, campaign.frequencyCap)}`}>
                      {campAnalytics.averageFrequency.toFixed(1)}
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm text-muted-foreground">Capped Users</Label>
                    <div className="text-xl font-semibold">
                      {campAnalytics.cappedUsers}
                      {campAnalytics.totalUsers > 0 && (
                        <span className="text-sm text-muted-foreground ml-1">
                          ({((campAnalytics.cappedUsers / campAnalytics.totalUsers) * 100).toFixed(1)}%)
                        </span>
                      )}
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm text-muted-foreground">Frequency Health</Label>
                    <div className="flex items-center space-x-1">
                      {campAnalytics.averageFrequency <= 3 ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : campAnalytics.averageFrequency <= 5 ? (
                        <Clock className="h-4 w-4 text-yellow-500" />
                      ) : (
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                      )}
                      <span className="text-sm">
                        {campAnalytics.averageFrequency <= 3 ? "Optimal" : 
                         campAnalytics.averageFrequency <= 5 ? "Good" : "High"}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Frequency Distribution */}
                {campAnalytics.frequencyDistribution.length > 0 && (
                  <div className="mt-4">
                    <Label className="text-sm text-muted-foreground mb-2 block">Frequency Distribution</Label>
                    <div className="space-y-2">
                      {campAnalytics.frequencyDistribution.slice(0, 5).map(({ frequency, users }) => (
                        <div key={frequency} className="flex items-center space-x-2">
                          <span className="text-sm w-16">{frequency} views:</span>
                          <Progress 
                            value={(users / campAnalytics.totalUsers) * 100} 
                            className="flex-1"
                          />
                          <span className="text-sm text-muted-foreground w-16">
                            {users} users
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                Frequency Configuration
              </CardTitle>
              <CardDescription>
                Set frequency caps to control how often users see your ads
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="campaignSelect">Select Campaign</Label>
                <Select 
                  value={selectedCampaign} 
                  onValueChange={(value) => {
                    setSelectedCampaign(value)
                    const campaign = analytics?.campaigns.find(c => c.campaign.id === value)?.campaign
                    if (campaign) {
                      setFrequencySettings({
                        frequencyCap: campaign.frequencyCap,
                        frequencyPeriod: campaign.frequencyPeriod
                      })
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a campaign" />
                  </SelectTrigger>
                  <SelectContent>
                    {analytics?.campaigns.map(({ campaign }) => (
                      <SelectItem key={campaign.id} value={campaign.id}>
                        {campaign.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="frequencyCap">Frequency Cap</Label>
                  <Input
                    id="frequencyCap"
                    type="number"
                    min="1"
                    max="50"
                    value={frequencySettings.frequencyCap}
                    onChange={(e) => setFrequencySettings({
                      ...frequencySettings, 
                      frequencyCap: parseInt(e.target.value) || 1
                    })}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Maximum impressions per user (1-50)
                  </p>
                </div>

                <div>
                  <Label htmlFor="frequencyPeriod">Frequency Period</Label>
                  <Select 
                    value={frequencySettings.frequencyPeriod} 
                    onValueChange={(value) => setFrequencySettings({
                      ...frequencySettings, 
                      frequencyPeriod: value
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DAILY">Daily</SelectItem>
                      <SelectItem value="WEEKLY">Weekly</SelectItem>
                      <SelectItem value="CAMPAIGN">Campaign Duration</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground mt-1">
                    Time period for frequency cap
                  </p>
                </div>
              </div>

              {/* Frequency Recommendations */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-sm mb-2">💡 Frequency Recommendations</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• <strong>Brand Awareness:</strong> 3-5 impressions per day</li>
                  <li>• <strong>Performance Campaigns:</strong> 2-3 impressions per day</li>
                  <li>• <strong>Retargeting:</strong> 5-8 impressions per week</li>
                  <li>• <strong>High-Value Products:</strong> 1-2 impressions per day</li>
                </ul>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={fetchFrequencyAnalytics}>
                  Reset
                </Button>
                <Button onClick={updateFrequencySettings} disabled={updating || !selectedCampaign}>
                  {updating ? "Updating..." : "Save Settings"}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {selectedCampaignData && (
            <Card>
              <CardHeader>
                <CardTitle>Detailed Analytics: {selectedCampaignData.campaign.name}</CardTitle>
                <CardDescription>
                  In-depth frequency analysis and user behavior
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Top Frequency Users */}
                {selectedCampaignData.analytics.topFrequencyUsers.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-3">Top Frequency Users</h4>
                    <div className="space-y-2">
                      {selectedCampaignData.analytics.topFrequencyUsers.map((user, index) => (
                        <div key={user.fingerprint} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-mono">{user.fingerprint}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm">{user.impressions} impressions</span>
                            <Badge 
                              variant={user.impressions >= selectedCampaignData.campaign.frequencyCap ? "destructive" : "default"}
                            >
                              {user.impressions >= selectedCampaignData.campaign.frequencyCap ? "Capped" : "Active"}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
