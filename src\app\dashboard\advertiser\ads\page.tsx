"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { AdvertiserLayout } from "@/components/dashboard/advertiser-layout"
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  MousePointer,
  DollarSign,
  Image as ImageIcon,
  Video,
  FileText,
  ExternalLink,
  Copy
} from "lucide-react"
import { toast } from "sonner"
import { AdFormat, Ad, Campaign } from "@/lib/types"

export default function AdsPage() {
  const [ads, setAds] = useState<Ad[]>([])
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [filteredAds, setFilteredAds] = useState<Ad[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [formatFilter, setFormatFilter] = useState<string>("all")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [newAd, setNewAd] = useState({
    campaignId: "",
    title: "",
    description: "",
    imageUrl: "",
    videoUrl: "",
    clickUrl: "",
    format: "BANNER" as AdFormat,
    width: 728,
    height: 90,
  })

  useEffect(() => {
    fetchAds()
    fetchCampaigns()
  }, [])

  useEffect(() => {
    filterAds()
  }, [ads, searchTerm, formatFilter])

  const fetchAds = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("/api/advertiser/ads")

      if (!response.ok) {
        throw new Error("Failed to fetch ads")
      }

      const data = await response.json()
      setAds(data.ads || [])
    } catch (error) {
      console.error("Failed to fetch ads:", error)
      toast.error("Failed to load ads")
    } finally {
      setIsLoading(false)
    }
  }

  const fetchCampaigns = async () => {
    try {
      const response = await fetch("/api/advertiser/campaigns")

      if (!response.ok) {
        throw new Error("Failed to fetch campaigns")
      }

      const data = await response.json()
      setCampaigns(data.campaigns || [])
    } catch (error) {
      console.error("Failed to fetch campaigns:", error)
      toast.error("Failed to load campaigns")
    }
  }

  const filterAds = () => {
    let filtered = ads

    if (searchTerm) {
      filtered = filtered.filter(ad =>
        ad.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (ad.campaignName && ad.campaignName.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    if (formatFilter !== "all") {
      filtered = filtered.filter(ad => ad.format === formatFilter)
    }

    setFilteredAds(filtered)
  }

  const handleCreateAd = async () => {
    try {
      // Validate form
      if (!newAd.title || !newAd.clickUrl || !newAd.campaignId) {
        toast.error("Please fill in all required fields")
        return
      }

      // Validate campaign selection
      if (!newAd.campaignId || newAd.campaignId === "no-campaigns" || !campaigns.find(c => c.id === newAd.campaignId)) {
        toast.error("Please select a valid campaign")
        return
      }

      // Validate format-specific requirements
      if (newAd.format === "VIDEO" && !newAd.videoUrl) {
        toast.error("Video URL is required for video ads")
        return
      }

      if (newAd.format !== "VIDEO" && !newAd.imageUrl) {
        toast.error("Image URL is required for non-video ads")
        return
      }

      // Prepare the ad data, removing empty URLs
      const adData = {
        ...newAd,
        imageUrl: newAd.imageUrl || undefined,
        videoUrl: newAd.videoUrl || undefined,
      }

      const response = await fetch("/api/advertiser/ads", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(adData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to create ad")
      }

      const data = await response.json()

      // Refresh ads list
      await fetchAds()

      setIsCreateDialogOpen(false)
      setNewAd({
        campaignId: "",
        title: "",
        description: "",
        imageUrl: "",
        videoUrl: "",
        clickUrl: "",
        format: "BANNER",
        width: 728,
        height: 90,
      })
      toast.success("Ad created successfully!")
    } catch (error) {
      console.error("Failed to create ad:", error)
      toast.error(error instanceof Error ? error.message : "Failed to create ad")
    }
  }

  const toggleAdStatus = async (id: string, isActive: boolean) => {
    try {
      // Make API call to update ad status
      const response = await fetch(`/api/advertiser/ads/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ isActive }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to update ad status")
      }

      const data = await response.json()

      // Update local state with the response data
      setAds(ads.map(ad =>
        ad.id === id ? { ...ad, isActive: data.ad?.isActive ?? isActive } : ad
      ))

      toast.success(`Ad ${isActive ? "activated" : "deactivated"} successfully!`)
    } catch (error) {
      console.error("Failed to update ad:", error)
      toast.error(error instanceof Error ? error.message : "Failed to update ad")
    }
  }

  const duplicateAd = async (ad: Ad) => {
    try {
      const duplicated: Ad = {
        ...ad,
        id: Date.now().toString(),
        title: `${ad.title} (Copy)`,
        isActive: false,
        impressions: 0,
        clicks: 0,
        conversions: 0,
        createdAt: new Date(),
      }
      setAds([duplicated, ...ads])
      toast.success("Ad duplicated successfully!")
    } catch (error) {
      console.error("Failed to duplicate ad:", error)
      toast.error("Failed to duplicate ad")
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US").format(num)
  }

  const getFormatIcon = (format: AdFormat) => {
    switch (format) {
      case "BANNER":
        return <ImageIcon className="h-4 w-4" />
      case "VIDEO":
        return <Video className="h-4 w-4" />
      case "NATIVE":
        return <FileText className="h-4 w-4" />
      case "POPUP":
        return <ExternalLink className="h-4 w-4" />
      default:
        return <ImageIcon className="h-4 w-4" />
    }
  }

  const getFormatBadge = (format: AdFormat) => {
    const colors = {
      BANNER: "bg-blue-100 text-blue-800",
      VIDEO: "bg-purple-100 text-purple-800",
      NATIVE: "bg-green-100 text-green-800",
      POPUP: "bg-orange-100 text-orange-800",
    }
    return (
      <Badge className={colors[format]}>
        {getFormatIcon(format)}
        <span className="ml-1">{format}</span>
      </Badge>
    )
  }

  return (
    <AdvertiserLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Ads</h2>
            <p className="text-muted-foreground">
              Create and manage your ad creatives
            </p>
          </div>
          
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create Ad
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Create New Ad</DialogTitle>
                <DialogDescription>
                  Design a new ad creative for your campaigns
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="campaignId">Campaign</Label>
                  <Select
                    value={newAd.campaignId}
                    onValueChange={(value) => setNewAd({ ...newAd, campaignId: value })}
                    disabled={campaigns.length === 0}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={campaigns.length === 0 ? "No campaigns available" : "Select campaign"} />
                    </SelectTrigger>
                    <SelectContent>
                      {campaigns.length > 0 ? (
                        campaigns.map((campaign) => (
                          <SelectItem key={campaign.id} value={campaign.id}>
                            {campaign.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-campaigns" disabled>
                          No campaigns available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  {campaigns.length === 0 && (
                    <p className="text-sm text-muted-foreground">
                      You need to create a campaign first before creating ads.{" "}
                      <Link href="/dashboard/advertiser/campaigns/create" className="text-primary hover:underline">
                        Create a campaign
                      </Link>
                    </p>
                  )}
                </div>
                
                <div className="grid gap-2">
                  <Label htmlFor="title">Ad Title</Label>
                  <Input
                    id="title"
                    placeholder="Enter ad title"
                    value={newAd.title}
                    onChange={(e) => setNewAd({ ...newAd, title: e.target.value })}
                  />
                </div>
                
                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Enter ad description"
                    value={newAd.description}
                    onChange={(e) => setNewAd({ ...newAd, description: e.target.value })}
                    rows={3}
                  />
                </div>
                
                <div className="grid gap-2">
                  <Label htmlFor="imageUrl">Image URL {newAd.format !== "VIDEO" && "*"}</Label>
                  <Input
                    id="imageUrl"
                    type="url"
                    placeholder="https://example.com/image.jpg"
                    value={newAd.imageUrl}
                    onChange={(e) => setNewAd({ ...newAd, imageUrl: e.target.value })}
                    disabled={newAd.format === "VIDEO"}
                  />
                  {newAd.format !== "VIDEO" && (
                    <p className="text-xs text-muted-foreground">
                      Required for banner, native, and popup ads
                    </p>
                  )}
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="videoUrl">Video URL {newAd.format === "VIDEO" && "*"}</Label>
                  <Input
                    id="videoUrl"
                    type="url"
                    placeholder="https://example.com/video.mp4"
                    value={newAd.videoUrl}
                    onChange={(e) => setNewAd({ ...newAd, videoUrl: e.target.value })}
                    disabled={newAd.format !== "VIDEO"}
                  />
                  {newAd.format === "VIDEO" && (
                    <p className="text-xs text-muted-foreground">
                      Required for video ads
                    </p>
                  )}
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="clickUrl">Click URL *</Label>
                  <Input
                    id="clickUrl"
                    type="url"
                    placeholder="https://example.com"
                    value={newAd.clickUrl}
                    onChange={(e) => setNewAd({ ...newAd, clickUrl: e.target.value })}
                  />
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="format">Format</Label>
                    <Select
                      value={newAd.format}
                      onValueChange={(value) => setNewAd({ ...newAd, format: value as AdFormat })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="BANNER">Banner</SelectItem>
                        <SelectItem value="VIDEO">Video</SelectItem>
                        <SelectItem value="NATIVE">Native</SelectItem>
                        <SelectItem value="POPUP">Popup</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="grid gap-2">
                    <Label htmlFor="width">Width (px)</Label>
                    <Input
                      id="width"
                      type="number"
                      value={newAd.width}
                      onChange={(e) => setNewAd({ ...newAd, width: parseInt(e.target.value) })}
                    />
                  </div>
                  
                  <div className="grid gap-2">
                    <Label htmlFor="height">Height (px)</Label>
                    <Input
                      id="height"
                      type="number"
                      value={newAd.height}
                      onChange={(e) => setNewAd({ ...newAd, height: parseInt(e.target.value) })}
                    />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="submit"
                  onClick={handleCreateAd}
                  disabled={
                    campaigns.length === 0 ||
                    !newAd.campaignId ||
                    newAd.campaignId === "no-campaigns" ||
                    !newAd.title ||
                    !newAd.clickUrl ||
                    (newAd.format === "VIDEO" && !newAd.videoUrl) ||
                    (newAd.format !== "VIDEO" && !newAd.imageUrl)
                  }
                >
                  Create Ad
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search ads..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <Select value={formatFilter} onValueChange={setFormatFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Formats</SelectItem>
                  <SelectItem value="BANNER">Banner</SelectItem>
                  <SelectItem value="VIDEO">Video</SelectItem>
                  <SelectItem value="NATIVE">Native</SelectItem>
                  <SelectItem value="POPUP">Popup</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Ads Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredAds.map((ad) => (
            <Card key={ad.id} className="overflow-hidden">
              <div className="aspect-video bg-muted flex items-center justify-center">
                {ad.imageUrl ? (
                  <img
                    src={ad.imageUrl}
                    alt={ad.title}
                    className="w-full h-full object-cover"
                  />
                ) : ad.videoUrl ? (
                  <div className="flex items-center justify-center">
                    <Video className="h-12 w-12 text-muted-foreground" />
                    <span className="ml-2 text-muted-foreground">Video Ad</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <ImageIcon className="h-12 w-12 text-muted-foreground" />
                    <span className="ml-2 text-muted-foreground">No Preview</span>
                  </div>
                )}
              </div>
              
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{ad.title}</CardTitle>
                    <CardDescription className="text-sm">
                      {ad.campaignName}
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getFormatBadge(ad.format)}
                    <Switch
                      checked={ad.isActive}
                      onCheckedChange={(checked) => toggleAdStatus(ad.id, checked)}
                    />
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="text-sm">
                  <p className="text-muted-foreground line-clamp-2">
                    {ad.description || "No description"}
                  </p>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-medium">{formatNumber(ad.impressions || 0)}</p>
                    <p className="text-muted-foreground">Impressions</p>
                  </div>
                  <div>
                    <p className="font-medium">{formatNumber(ad.clicks || 0)}</p>
                    <p className="text-muted-foreground">Clicks</p>
                  </div>
                  <div>
                    <p className="font-medium">{(ad.ctr || 0).toFixed(2)}%</p>
                    <p className="text-muted-foreground">CTR</p>
                  </div>
                  <div>
                    <p className="font-medium">{ad.conversions || 0}</p>
                    <p className="text-muted-foreground">Conversions</p>
                  </div>
                </div>
                
                <div className="flex items-center justify-between pt-2 border-t">
                  <span className="text-sm text-muted-foreground">
                    {ad.width} × {ad.height}
                  </span>
                  <div className="flex items-center space-x-1">
                    <Button variant="outline" size="icon" title="View">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon" title="Edit">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => duplicateAd(ad)}
                      title="Duplicate"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Summary Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Ads</CardTitle>
              <ImageIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{ads.length}</div>
              <p className="text-xs text-muted-foreground">
                {ads.filter(ad => ad.isActive).length} active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Impressions</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(ads.reduce((sum, ad) => sum + (ad.impressions || 0), 0))}
              </div>
              <p className="text-xs text-muted-foreground">
                Across all ads
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
              <MousePointer className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(ads.reduce((sum, ad) => sum + (ad.clicks || 0), 0))}
              </div>
              <p className="text-xs text-muted-foreground">
                Total engagement
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Conversions</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {ads.reduce((sum, ad) => sum + (ad.conversions || 0), 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                Total conversions
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdvertiserLayout>
  )
}
