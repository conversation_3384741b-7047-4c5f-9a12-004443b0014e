<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AdNetwork SDK Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .ad-container {
            border: 2px dashed #ccc;
            margin: 10px 0;
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fafafa;
        }
        .log {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            margin: 5px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AdNetwork SDK Test Page</h1>
        
        <!-- SDK Status -->
        <div class="test-section">
            <h2>1. SDK Status</h2>
            <div id="sdk-status" class="status">Checking...</div>
            <div id="sdk-log" class="log"></div>
        </div>

        <!-- API Key Configuration -->
        <div class="test-section">
            <h2>2. API Key Configuration</h2>
            <div>
                <label>Publisher API Key:</label><br>
                <input type="text" id="api-key" placeholder="Enter your publisher API key">
                <button onclick="initializeSDK()">Initialize SDK</button>
            </div>
            <div id="init-status" class="status" style="display: none;"></div>
        </div>

        <!-- Ad Space Testing -->
        <div class="test-section">
            <h2>3. Ad Space Testing</h2>
            
            <h3>Header Banner (728x90)</h3>
            <div id="header-ad" class="ad-container" style="width: 728px; height: 90px;">
                <span>Header Ad Space</span>
            </div>
            <button onclick="loadHeaderAd()">Load Header Ad</button>

            <h3>Sidebar Banner (300x250)</h3>
            <div id="sidebar-ad" class="ad-container" style="width: 300px; height: 250px;">
                <span>Sidebar Ad Space</span>
            </div>
            <button onclick="loadSidebarAd()">Load Sidebar Ad</button>

            <h3>Video Ad (640x360)</h3>
            <div id="video-ad" class="ad-container" style="width: 640px; height: 360px;">
                <span>Video Ad Space</span>
            </div>
            <button onclick="loadVideoAd()">Load Video Ad</button>
        </div>

        <!-- Debug Information -->
        <div class="test-section">
            <h2>4. Debug Information</h2>
            <div id="debug-log" class="log"></div>
            <button onclick="clearDebugLog()">Clear Log</button>
            <button onclick="testAPIConnection()">Test API Connection</button>
        </div>
    </div>

    <!-- Load AdNetwork SDK -->
    <script src="/sdk/adnetwork-sdk.js"></script>
    
    <script>
        let debugLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            debugLog.push(logEntry);
            
            const debugLogElement = document.getElementById('debug-log');
            debugLogElement.innerHTML = debugLog.join('\n');
            debugLogElement.scrollTop = debugLogElement.scrollHeight;
            
            console.log(`[AdNetwork Test] ${message}`);
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
        }

        function clearDebugLog() {
            debugLog = [];
            document.getElementById('debug-log').innerHTML = '';
        }

        // Check SDK availability
        function checkSDKStatus() {
            if (typeof window.AdNetwork !== 'undefined') {
                updateStatus('sdk-status', '✅ AdNetwork SDK loaded successfully', 'success');
                log('SDK loaded successfully');
                document.getElementById('sdk-log').innerHTML = `
                    SDK Version: ${window.AdNetwork.version || 'Unknown'}<br>
                    Available methods: ${Object.keys(window.AdNetwork).join(', ')}
                `;
                return true;
            } else {
                updateStatus('sdk-status', '❌ AdNetwork SDK failed to load', 'error');
                log('SDK failed to load', 'error');
                return false;
            }
        }

        // Initialize SDK with API key
        function initializeSDK() {
            const apiKey = document.getElementById('api-key').value.trim();
            
            if (!apiKey) {
                updateStatus('init-status', '❌ Please enter an API key', 'error');
                return;
            }

            try {
                const result = window.AdNetwork.init({
                    apiKey: apiKey,
                    apiUrl: 'http://localhost:3000/api',
                    debug: true
                });

                if (result) {
                    updateStatus('init-status', '✅ SDK initialized successfully', 'success');
                    log(`SDK initialized with API key: ${apiKey.substring(0, 10)}...`);
                } else {
                    updateStatus('init-status', '❌ SDK initialization failed', 'error');
                    log('SDK initialization failed', 'error');
                }
            } catch (error) {
                updateStatus('init-status', `❌ Error: ${error.message}`, 'error');
                log(`Initialization error: ${error.message}`, 'error');
            }
        }

        // Load header ad
        async function loadHeaderAd() {
            log('Loading header ad...');
            try {
                const result = await window.AdNetwork.loadAd('header-ad', {
                    format: 'BANNER',
                    width: 728,
                    height: 90,
                    position: 'header',
                    adSpaceId: 'test-header-space'
                });
                log(`Header ad load result: ${result}`);
            } catch (error) {
                log(`Header ad error: ${error.message}`, 'error');
            }
        }

        // Load sidebar ad
        async function loadSidebarAd() {
            log('Loading sidebar ad...');
            try {
                const result = await window.AdNetwork.loadAd('sidebar-ad', {
                    format: 'BANNER',
                    width: 300,
                    height: 250,
                    position: 'sidebar',
                    adSpaceId: 'test-sidebar-space'
                });
                log(`Sidebar ad load result: ${result}`);
            } catch (error) {
                log(`Sidebar ad error: ${error.message}`, 'error');
            }
        }

        // Load video ad
        async function loadVideoAd() {
            log('Loading video ad...');
            try {
                const result = await window.AdNetwork.loadAd('video-ad', {
                    format: 'VIDEO',
                    width: 640,
                    height: 360,
                    position: 'content',
                    adSpaceId: 'test-video-space'
                });
                log(`Video ad load result: ${result}`);
            } catch (error) {
                log(`Video ad error: ${error.message}`, 'error');
            }
        }

        // Test API connection
        async function testAPIConnection() {
            log('Testing API connection...');
            try {
                const response = await fetch('http://localhost:3000/api/health');
                if (response.ok) {
                    const data = await response.json();
                    log(`API connection successful: ${JSON.stringify(data)}`);
                } else {
                    log(`API connection failed: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`API connection error: ${error.message}`, 'error');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded, checking SDK status...');
            checkSDKStatus();
        });
    </script>
</body>
</html>
