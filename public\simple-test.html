<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Ad Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .ad-container { 
            border: 2px solid #007bff; 
            padding: 20px; 
            margin: 20px 0; 
            text-align: center; 
            background: #f8f9fa;
            border-radius: 8px;
        }
        .log { 
            background: #f0f0f0; 
            padding: 15px; 
            border-radius: 4px; 
            font-family: monospace; 
            font-size: 12px; 
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; font-weight: bold; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>Simple Ad Network Test</h1>
    
    <div id="status" class="status info">Initializing...</div>
    
    <!-- Test Ad Container -->
    <div id="test-ad" class="ad-container" style="width: 728px; height: 90px;">
        <p>Test Ad Container (728x90)</p>
        <p>Waiting for ad to load...</p>
    </div>
    
    <div>
        <button onclick="testSDK()">Test SDK</button>
        <button onclick="testAPI()">Test API</button>
        <button onclick="loadAd()">Load Ad</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div id="log" class="log">Starting test...\n</div>

    <!-- Load SDK -->
    <script src="/sdk/adnetwork-sdk.js"></script>
    
    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[Test] ${message}`);
        }
        
        function setStatus(message, type = 'info') {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        function clearLog() {
            logElement.textContent = '';
        }
        
        function testSDK() {
            log('Testing SDK availability...');
            
            if (typeof window.AdNetwork === 'undefined') {
                log('❌ AdNetwork SDK not found', 'error');
                setStatus('❌ SDK not loaded', 'error');
                return false;
            }
            
            log('✅ AdNetwork SDK found');
            log(`SDK version: ${window.AdNetwork.version || 'unknown'}`);
            log(`Available methods: ${Object.keys(window.AdNetwork).join(', ')}`);
            setStatus('✅ SDK loaded successfully', 'success');
            return true;
        }
        
        async function testAPI() {
            log('Testing API connection...');
            
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    const data = await response.json();
                    log('✅ API connection successful');
                    log(`API status: ${data.status}`);
                    log(`Active campaigns: ${data.database?.stats?.activeCampaigns || 0}`);
                    log(`Active ad spaces: ${data.database?.stats?.activeAdSpaces || 0}`);
                    setStatus('✅ API connection successful', 'success');
                } else {
                    log(`❌ API error: ${response.status} ${response.statusText}`, 'error');
                    setStatus('❌ API connection failed', 'error');
                }
            } catch (error) {
                log(`❌ API connection failed: ${error.message}`, 'error');
                setStatus('❌ API connection failed', 'error');
            }
        }
        
        async function loadAd() {
            log('Testing ad loading...');
            
            if (!testSDK()) {
                return;
            }
            
            // Initialize SDK
            log('Initializing SDK...');
            const initResult = window.AdNetwork.init({
                apiKey: 'ak_8fbd99461863a1327678c56c184b4cd5024126fa30e000ff7374957de733ca29',
                apiUrl: '/api',
                debug: true
            });
            
            if (!initResult) {
                log('❌ SDK initialization failed', 'error');
                setStatus('❌ SDK initialization failed', 'error');
                return;
            }
            
            log('✅ SDK initialized successfully');
            
            // Test element selection
            const testElement = document.getElementById('test-ad');
            if (!testElement) {
                log('❌ Test element not found', 'error');
                return;
            }
            
            log('✅ Test element found');
            log(`Element ID: ${testElement.id}`);
            log(`Element dimensions: ${testElement.offsetWidth}x${testElement.offsetHeight}`);
            
            // Load ad
            log('Loading ad...');
            try {
                const result = await window.AdNetwork.loadAd('test-ad', {
                    format: 'BANNER',
                    width: 728,
                    height: 90,
                    position: 'header',
                    adSpaceId: '68aed2cb354bb17742caa661'
                });
                
                log(`Ad load result: ${result}`);
                if (result) {
                    setStatus('✅ Ad loaded successfully', 'success');
                } else {
                    setStatus('⚠️ Ad load returned false', 'error');
                }
                
            } catch (error) {
                log(`❌ Ad loading error: ${error.message}`, 'error');
                log(`Error stack: ${error.stack}`);
                setStatus('❌ Ad loading failed', 'error');
            }
        }
        
        // Auto-run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded, running initial tests...');
            testSDK();
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
