import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Only admins can access this endpoint" },
        { status: 403 }
      )
    }

    // Get system-wide metrics
    const [
      totalCampaigns,
      activeCampaigns,
      totalAds,
      activeAds,
      totalPublishers,
      activePublishers,
      totalAdvertisers,
      activeAdvertisers,
      totalAdSpaces,
      activeAdSpaces,
      totalPlacements,
      activePlacements
    ] = await Promise.all([
      prisma.campaign.count(),
      prisma.campaign.count({ where: { status: "ACTIVE" } }),
      prisma.ad.count(),
      prisma.ad.count({ where: { isActive: true } }),
      prisma.publisherProfile.count(),
      prisma.publisherProfile.count({ where: { isActive: true } }),
      prisma.advertiserProfile.count(),
      prisma.advertiserProfile.count({ where: { balance: { gt: 0 } } }),
      prisma.adSpace.count(),
      prisma.adSpace.count({ where: { isActive: true } }),
      prisma.adPlacement.count(),
      prisma.adPlacement.count({ where: { isActive: true } })
    ])

    // Get aggregated placement metrics
    const placementMetrics = await prisma.adPlacement.aggregate({
      _sum: {
        impressions: true,
        clicks: true,
        conversions: true,
        cost: true,
        revenue: true
      }
    })

    // Calculate derived metrics
    const totalImpressions = placementMetrics._sum.impressions || 0
    const totalClicks = placementMetrics._sum.clicks || 0
    const totalConversions = placementMetrics._sum.conversions || 0
    const totalCost = placementMetrics._sum.cost || 0
    const totalRevenue = placementMetrics._sum.revenue || 0

    const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0
    const conversionRate = totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0
    const averageCPC = totalClicks > 0 ? totalCost / totalClicks : 0
    const averageCPM = totalImpressions > 0 ? (totalCost / totalImpressions) * 1000 : 0

    // Get recent activity (last 24 hours)
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)

    const recentPlacements = await prisma.adPlacement.findMany({
      where: {
        updatedAt: {
          gte: yesterday
        }
      },
      include: {
        campaign: {
          select: {
            name: true
          }
        },
        ad: {
          select: {
            title: true
          }
        },
        adSpace: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        updatedAt: 'desc'
      },
      take: 10
    })

    // Get top performing campaigns
    const topCampaigns = await prisma.campaign.findMany({
      where: {
        status: "ACTIVE"
      },
      include: {
        adPlacements: {
          select: {
            impressions: true,
            clicks: true,
            conversions: true,
            cost: true,
            revenue: true
          }
        }
      },
      take: 10
    })

    const topCampaignsWithMetrics = topCampaigns.map(campaign => {
      const totalImpressions = campaign.adPlacements.reduce((sum, p) => sum + p.impressions, 0)
      const totalClicks = campaign.adPlacements.reduce((sum, p) => sum + p.clicks, 0)
      const totalConversions = campaign.adPlacements.reduce((sum, p) => sum + p.conversions, 0)
      const totalCost = campaign.adPlacements.reduce((sum, p) => sum + p.cost, 0)
      const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0

      return {
        id: campaign.id,
        name: campaign.name,
        status: campaign.status,
        impressions: totalImpressions,
        clicks: totalClicks,
        conversions: totalConversions,
        cost: totalCost,
        ctr: parseFloat(ctr.toFixed(2))
      }
    }).sort((a, b) => b.impressions - a.impressions)

    // System health metrics
    const systemHealth = {
      databaseConnected: true, // If we got here, DB is connected
      biddingEngineStatus: "healthy",
      trackingSystemStatus: "healthy",
      fraudDetectionStatus: "active",
      lastOptimizationRun: new Date().toISOString()
    }

    const stats = {
      // Campaign metrics
      totalCampaigns,
      activeCampaigns,
      campaignActivationRate: totalCampaigns > 0 ? (activeCampaigns / totalCampaigns) * 100 : 0,
      
      // Ad metrics
      totalAds,
      activeAds,
      adActivationRate: totalAds > 0 ? (activeAds / totalAds) * 100 : 0,
      
      // User metrics
      totalPublishers,
      activePublishers,
      publisherActivationRate: totalPublishers > 0 ? (activePublishers / totalPublishers) * 100 : 0,
      totalAdvertisers,
      activeAdvertisers,
      advertiserActivationRate: totalAdvertisers > 0 ? (activeAdvertisers / totalAdvertisers) * 100 : 0,
      
      // Ad space metrics
      totalAdSpaces,
      activeAdSpaces,
      adSpaceActivationRate: totalAdSpaces > 0 ? (activeAdSpaces / totalAdSpaces) * 100 : 0,
      
      // Performance metrics
      totalImpressions,
      totalClicks,
      totalConversions,
      ctr: parseFloat(ctr.toFixed(2)),
      conversionRate: parseFloat(conversionRate.toFixed(2)),
      
      // Financial metrics
      totalRevenue: parseFloat(totalRevenue.toFixed(2)),
      totalCost: parseFloat(totalCost.toFixed(2)),
      profit: parseFloat((totalRevenue - totalCost).toFixed(2)),
      averageCPC: parseFloat(averageCPC.toFixed(2)),
      averageCPM: parseFloat(averageCPM.toFixed(2)),
      
      // System metrics
      totalPlacements,
      activePlacements,
      fillRate: totalAdSpaces > 0 ? (activePlacements / totalAdSpaces) * 100 : 0
    }

    return NextResponse.json({
      stats,
      recentActivity: recentPlacements.map(placement => ({
        id: placement.id,
        type: "placement_update",
        campaignName: placement.campaign.name,
        adTitle: placement.ad.title,
        adSpaceName: placement.adSpace.name,
        impressions: placement.impressions,
        clicks: placement.clicks,
        conversions: placement.conversions,
        timestamp: placement.updatedAt
      })),
      topCampaigns: topCampaignsWithMetrics,
      systemHealth
    })
  } catch (error) {
    console.error("Admin dashboard error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
