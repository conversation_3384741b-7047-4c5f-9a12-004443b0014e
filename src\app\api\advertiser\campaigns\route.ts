import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { z } from "zod"
import { CampaignStatus, PricingModel } from "@prisma/client"

const campaignCreateSchema = z.object({
  name: z.string().min(1, "Campaign name is required"),
  description: z.string().optional(),
  budget: z.number().min(1, "Budget must be greater than 0"),
  dailyBudget: z.number().min(1, "Daily budget must be greater than 0").optional(),
  pricingModel: z.enum(["CPC", "CPM", "CPA"]),
  bidAmount: z.number().min(0.01, "Bid amount must be greater than 0"),
  targetRegions: z.array(z.string()).min(1, "At least one target region is required"),
  targetCategories: z.array(z.string()).min(1, "At least one target category is required"),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// GET /api/advertiser/campaigns - List all campaigns for the advertiser
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get advertiser profile
    const advertiserProfile = await prisma.advertiserProfile.findUnique({
      where: { userId: session.user.id },
    })

    if (!advertiserProfile) {
      return NextResponse.json(
        { message: "Advertiser profile not found" },
        { status: 404 }
      )
    }

    // Get campaigns with metrics
    const campaigns = await prisma.campaign.findMany({
      where: { advertiserId: advertiserProfile.id },
      include: {
        ads: {
          select: {
            id: true,
            title: true,
            isActive: true,
          },
        },
        adPlacements: {
          select: {
            impressions: true,
            clicks: true,
            conversions: true,
            cost: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    })

    // Calculate metrics for each campaign
    const campaignsWithMetrics = campaigns.map((campaign) => {
      const totalImpressions = campaign.adPlacements.reduce((sum, p) => sum + p.impressions, 0)
      const totalClicks = campaign.adPlacements.reduce((sum, p) => sum + p.clicks, 0)
      const totalConversions = campaign.adPlacements.reduce((sum, p) => sum + p.conversions, 0)
      const totalSpent = campaign.adPlacements.reduce((sum, p) => sum + p.cost, 0)
      const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0
      const costPerClick = totalClicks > 0 ? totalSpent / totalClicks : 0
      const costPerConversion = totalConversions > 0 ? totalSpent / totalConversions : 0

      return {
        id: campaign.id,
        name: campaign.name,
        description: campaign.description,
        status: campaign.status,
        budget: campaign.budget,
        dailyBudget: campaign.dailyBudget,
        bidAmount: campaign.bidAmount,
        pricingModel: campaign.pricingModel,
        targetRegions: campaign.targetRegions,
        targetCategories: campaign.targetCategories,
        startDate: campaign.startDate,
        endDate: campaign.endDate,
        createdAt: campaign.createdAt,
        updatedAt: campaign.updatedAt,
        // Computed metrics
        spent: parseFloat(totalSpent.toFixed(2)),
        impressions: totalImpressions,
        clicks: totalClicks,
        conversions: totalConversions,
        ctr: parseFloat(ctr.toFixed(2)),
        costPerClick: parseFloat(costPerClick.toFixed(2)),
        costPerConversion: parseFloat(costPerConversion.toFixed(2)),
        remainingBudget: parseFloat((campaign.budget - totalSpent).toFixed(2)),
        adsCount: campaign.ads.length,
        activeAdsCount: campaign.ads.filter(ad => ad.isActive).length,
      }
    })

    return NextResponse.json({
      campaigns: campaignsWithMetrics,
    })
  } catch (error) {
    console.error("Campaigns list error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/advertiser/campaigns - Create a new campaign
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = campaignCreateSchema.parse(body)

    // Get advertiser profile
    const advertiserProfile = await prisma.advertiserProfile.findUnique({
      where: { userId: session.user.id },
    })

    if (!advertiserProfile) {
      return NextResponse.json(
        { message: "Advertiser profile not found" },
        { status: 404 }
      )
    }

    // Check if advertiser has sufficient balance
    if (advertiserProfile.balance < validatedData.budget) {
      return NextResponse.json(
        {
          message: "Insufficient balance to create campaign",
          error: "INSUFFICIENT_BALANCE",
          currentBalance: advertiserProfile.balance,
          requiredAmount: validatedData.budget,
          shortfall: validatedData.budget - advertiserProfile.balance
        },
        { status: 400 }
      )
    }

    // Create campaign
    const campaign = await prisma.campaign.create({
      data: {
        advertiserId: advertiserProfile.id,
        name: validatedData.name,
        description: validatedData.description,
        budget: validatedData.budget,
        dailyBudget: validatedData.dailyBudget,
        pricingModel: validatedData.pricingModel as PricingModel,
        bidAmount: validatedData.bidAmount,
        targetRegions: validatedData.targetRegions,
        targetCategories: validatedData.targetCategories,
        startDate: validatedData.startDate ? new Date(validatedData.startDate) : null,
        endDate: validatedData.endDate ? new Date(validatedData.endDate) : null,
        frequencyCap: validatedData.frequencyCap || 5,
        frequencyPeriod: validatedData.frequencyPeriod || "DAILY",
        status: "DRAFT",
      },
    })

    return NextResponse.json(
      {
        message: "Campaign created successfully",
        campaign: {
          id: campaign.id,
          name: campaign.name,
          status: campaign.status,
          budget: campaign.budget,
          pricingModel: campaign.pricingModel,
        },
      },
      { status: 201 }
    )
  } catch (error) {
    console.error("Campaign creation error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid request data", errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
