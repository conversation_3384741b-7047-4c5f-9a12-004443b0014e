import { prisma } from "@/lib/prisma"

interface ClickEvent {
  adId: string
  adSpaceId: string
  campaignId: string
  ipAddress: string
  userAgent: string
  timestamp: Date
}

interface ImpressionEvent {
  adId: string
  adSpaceId: string
  campaignId: string
  ipAddress: string
  userAgent: string
  timestamp: Date
}

interface FraudCheckResult {
  isValid: boolean
  reason?: string
  riskScore: number
}

class FraudPreventionEngine {
  private clickHistory = new Map<string, ClickEvent[]>()
  private impressionHistory = new Map<string, ImpressionEvent[]>()
  private suspiciousIPs = new Set<string>()
  
  // Time windows for fraud detection (in milliseconds)
  private readonly CLICK_WINDOW = 60 * 1000 // 1 minute
  private readonly IMPRESSION_WINDOW = 10 * 1000 // 10 seconds
  private readonly CLEANUP_INTERVAL = 5 * 60 * 1000 // 5 minutes

  constructor() {
    // Cleanup old entries periodically
    setInterval(() => this.cleanup(), this.CLEANUP_INTERVAL)
  }

  /**
   * Check if a click is valid (not fraudulent)
   */
  async validateClick(event: ClickEvent): Promise<FraudCheckResult> {
    const ipKey = `${event.ipAddress}:${event.adId}`
    const now = event.timestamp.getTime()
    
    // Get recent clicks from this IP for this ad
    const recentClicks = this.getRecentClicks(ipKey, now)
    
    // Rule 1: Multiple clicks from same IP within time window
    if (recentClicks.length >= 3) {
      this.markSuspiciousIP(event.ipAddress)
      return {
        isValid: false,
        reason: "Multiple clicks from same IP within time window",
        riskScore: 0.9
      }
    }

    // Rule 2: Click without recent impression (click fraud)
    const hasRecentImpression = await this.hasRecentImpression(event)
    if (!hasRecentImpression) {
      return {
        isValid: false,
        reason: "Click without recent impression",
        riskScore: 0.8
      }
    }

    // Rule 3: Suspicious user agent patterns
    const userAgentRisk = this.analyzeUserAgent(event.userAgent)
    if (userAgentRisk > 0.7) {
      return {
        isValid: false,
        reason: "Suspicious user agent pattern",
        riskScore: userAgentRisk
      }
    }

    // Rule 4: Known suspicious IP
    if (this.suspiciousIPs.has(event.ipAddress)) {
      return {
        isValid: false,
        reason: "Known suspicious IP address",
        riskScore: 0.85
      }
    }

    // Rule 5: Click velocity check (too fast clicking)
    const clickVelocity = this.calculateClickVelocity(recentClicks)
    if (clickVelocity > 10) { // More than 10 clicks per minute
      this.markSuspiciousIP(event.ipAddress)
      return {
        isValid: false,
        reason: "Abnormal click velocity",
        riskScore: 0.75
      }
    }

    // Store the click for future validation
    this.storeClick(ipKey, event)

    // Calculate overall risk score
    const riskScore = Math.max(userAgentRisk, clickVelocity / 20, 0.1)

    return {
      isValid: true,
      riskScore
    }
  }

  /**
   * Check if an impression is valid
   */
  async validateImpression(event: ImpressionEvent): Promise<FraudCheckResult> {
    const ipKey = `${event.ipAddress}:${event.adId}`
    const now = event.timestamp.getTime()
    
    // Get recent impressions from this IP for this ad
    const recentImpressions = this.getRecentImpressions(ipKey, now)
    
    // Rule 1: Too many impressions from same IP
    if (recentImpressions.length >= 10) {
      this.markSuspiciousIP(event.ipAddress)
      return {
        isValid: false,
        reason: "Too many impressions from same IP",
        riskScore: 0.8
      }
    }

    // Rule 2: Suspicious user agent
    const userAgentRisk = this.analyzeUserAgent(event.userAgent)
    if (userAgentRisk > 0.8) {
      return {
        isValid: false,
        reason: "Suspicious user agent for impression",
        riskScore: userAgentRisk
      }
    }

    // Store the impression
    this.storeImpression(ipKey, event)

    return {
      isValid: true,
      riskScore: Math.max(userAgentRisk, 0.1)
    }
  }

  /**
   * Get recent clicks for an IP-Ad combination
   */
  private getRecentClicks(ipKey: string, currentTime: number): ClickEvent[] {
    const clicks = this.clickHistory.get(ipKey) || []
    return clicks.filter(click => 
      currentTime - click.timestamp.getTime() <= this.CLICK_WINDOW
    )
  }

  /**
   * Get recent impressions for an IP-Ad combination
   */
  private getRecentImpressions(ipKey: string, currentTime: number): ImpressionEvent[] {
    const impressions = this.impressionHistory.get(ipKey) || []
    return impressions.filter(impression => 
      currentTime - impression.timestamp.getTime() <= this.IMPRESSION_WINDOW
    )
  }

  /**
   * Check if there was a recent impression before this click
   */
  private async hasRecentImpression(clickEvent: ClickEvent): Promise<boolean> {
    const ipKey = `${clickEvent.ipAddress}:${clickEvent.adId}`
    const recentImpressions = this.getRecentImpressions(ipKey, clickEvent.timestamp.getTime())
    
    // Also check database for impressions in the last 5 minutes
    const fiveMinutesAgo = new Date(clickEvent.timestamp.getTime() - 5 * 60 * 1000)
    
    try {
      const dbImpressions = await prisma.adPlacement.findMany({
        where: {
          adId: clickEvent.adId,
          adSpaceId: clickEvent.adSpaceId,
          updatedAt: {
            gte: fiveMinutesAgo
          }
        },
        take: 1
      })
      
      return recentImpressions.length > 0 || dbImpressions.length > 0
    } catch (error) {
      console.error("Error checking recent impressions:", error)
      return recentImpressions.length > 0
    }
  }

  /**
   * Analyze user agent for suspicious patterns
   */
  private analyzeUserAgent(userAgent: string): number {
    if (!userAgent || userAgent.length < 10) {
      return 0.9 // Very suspicious
    }

    // Check for bot patterns
    const botPatterns = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /curl/i, /wget/i, /python/i, /java/i
    ]

    for (const pattern of botPatterns) {
      if (pattern.test(userAgent)) {
        return 0.95
      }
    }

    // Check for very old or suspicious browsers
    if (userAgent.includes("MSIE 6") || userAgent.includes("MSIE 7")) {
      return 0.7
    }

    return 0.1 // Low risk
  }

  /**
   * Calculate click velocity (clicks per minute)
   */
  private calculateClickVelocity(recentClicks: ClickEvent[]): number {
    if (recentClicks.length < 2) return 0
    
    const timeSpan = this.CLICK_WINDOW / (60 * 1000) // Convert to minutes
    return recentClicks.length / timeSpan
  }

  /**
   * Store a click event
   */
  private storeClick(ipKey: string, event: ClickEvent): void {
    if (!this.clickHistory.has(ipKey)) {
      this.clickHistory.set(ipKey, [])
    }
    this.clickHistory.get(ipKey)!.push(event)
  }

  /**
   * Store an impression event
   */
  private storeImpression(ipKey: string, event: ImpressionEvent): void {
    if (!this.impressionHistory.has(ipKey)) {
      this.impressionHistory.set(ipKey, [])
    }
    this.impressionHistory.get(ipKey)!.push(event)
  }

  /**
   * Mark an IP as suspicious
   */
  private markSuspiciousIP(ipAddress: string): void {
    this.suspiciousIPs.add(ipAddress)
    console.log(`Marked IP as suspicious: ${ipAddress}`)
  }

  /**
   * Clean up old entries to prevent memory leaks
   */
  private cleanup(): void {
    const now = Date.now()
    
    // Clean click history
    for (const [key, clicks] of this.clickHistory.entries()) {
      const validClicks = clicks.filter(click => 
        now - click.timestamp.getTime() <= this.CLICK_WINDOW * 2
      )
      
      if (validClicks.length === 0) {
        this.clickHistory.delete(key)
      } else {
        this.clickHistory.set(key, validClicks)
      }
    }

    // Clean impression history
    for (const [key, impressions] of this.impressionHistory.entries()) {
      const validImpressions = impressions.filter(impression => 
        now - impression.timestamp.getTime() <= this.IMPRESSION_WINDOW * 2
      )
      
      if (validImpressions.length === 0) {
        this.impressionHistory.delete(key)
      } else {
        this.impressionHistory.set(key, validImpressions)
      }
    }

    console.log(`Fraud prevention cleanup completed. Active entries: ${this.clickHistory.size + this.impressionHistory.size}`)
  }

  /**
   * Get fraud statistics
   */
  getStats() {
    return {
      activeClickTracking: this.clickHistory.size,
      activeImpressionTracking: this.impressionHistory.size,
      suspiciousIPs: this.suspiciousIPs.size
    }
  }
}

// Export singleton instance
export const fraudPrevention = new FraudPreventionEngine()

// Export types
export type { ClickEvent, ImpressionEvent, FraudCheckResult }
