import { prisma } from "@/lib/prisma"
import crypto from "crypto"

interface ClickData {
  campaignId: string
  adId: string
  adSpaceId: string
  publisherId: string
  advertiserId: string
  ipAddress: string
  userAgent?: string
  referrer?: string
  destinationUrl: string
  bidAmount: number
}

interface DeviceInfo {
  deviceType: string
  browser: string
  os: string
}

interface GeoLocation {
  country?: string
  region?: string
  city?: string
}

export class EnhancedClickTracker {
  private static instance: EnhancedClickTracker
  private fraudDetectionCache = new Map<string, number>()
  private clickFrequencyCache = new Map<string, number[]>()
  private blockedIPs = new Set<string>()

  static getInstance(): EnhancedClickTracker {
    if (!EnhancedClickTracker.instance) {
      EnhancedClickTracker.instance = new EnhancedClickTracker()
      // Clear blocked IPs every hour
      setInterval(() => {
        EnhancedClickTracker.instance.clearBlockedIPs()
      }, 3600000) // 1 hour
    }
    return EnhancedClickTracker.instance
  }

  /**
   * Clear blocked IPs (called periodically)
   */
  private clearBlockedIPs(): void {
    const count = this.blockedIPs.size
    this.blockedIPs.clear()
    if (count > 0) {
      console.log(`🔄 Cleared ${count} blocked IPs`)
    }
  }

  /**
   * Track a click with comprehensive fraud detection and fair pricing
   */
  async trackClick(clickData: ClickData): Promise<{
    success: boolean
    trackingId?: string
    isFraudulent?: boolean
    fraudReason?: string
    publisherRevenue?: number
    platformRevenue?: number
  }> {
    try {
      // 1. Generate unique tracking ID
      const trackingId = this.generateTrackingId(clickData)

      // 2. Check for duplicate clicks
      const existingClick = await prisma.clickTracking.findUnique({
        where: { trackingId }
      })

      if (existingClick) {
        return {
          success: false,
          isFraudulent: true,
          fraudReason: "Duplicate click detected"
        }
      }

      // 3. Fraud detection
      const fraudCheck = await this.detectFraud(clickData)
      if (fraudCheck.isFraudulent) {
        // Still record the click but mark as fraudulent
        await this.recordClick(clickData, trackingId, true, fraudCheck.reason)
        return {
          success: false,
          isFraudulent: true,
          fraudReason: fraudCheck.reason
        }
      }

      // 4. Calculate fair revenue split
      const revenueData = await this.calculateRevenueSplit(clickData)

      // 5. Record valid click
      await this.recordClick(clickData, trackingId, false, null, revenueData)

      // 6. Update real-time metrics
      await this.updateMetrics(clickData, revenueData)

      // 7. Trigger pacing adjustments
      await this.triggerPacingAdjustments(clickData)

      return {
        success: true,
        trackingId,
        isFraudulent: false,
        publisherRevenue: revenueData.publisherRevenue,
        platformRevenue: revenueData.platformRevenue
      }

    } catch (error) {
      console.error("Enhanced click tracking error:", error)
      return {
        success: false,
        isFraudulent: false,
        fraudReason: "Internal tracking error"
      }
    }
  }

  /**
   * Generate unique tracking ID
   */
  private generateTrackingId(clickData: ClickData): string {
    const data = `${clickData.campaignId}_${clickData.adId}_${clickData.adSpaceId}_${Date.now()}`
    return crypto.createHash('sha256').update(data).digest('hex').substring(0, 32)
  }

  /**
   * Advanced fraud detection
   */
  private async detectFraud(clickData: ClickData): Promise<{
    isFraudulent: boolean
    reason?: string
  }> {
    const { ipAddress, userAgent, campaignId } = clickData

    // 0. Check if IP is already blocked
    if (this.blockedIPs.has(ipAddress)) {
      return {
        isFraudulent: true,
        reason: "IP address is blocked due to previous fraud"
      }
    }

    // 1. IP-based fraud detection (more aggressive)
    const ipKey = `${ipAddress}_${campaignId}`
    const recentClicks = this.clickFrequencyCache.get(ipKey) || []
    const now = Date.now()

    // Remove clicks older than 1 hour
    const validClicks = recentClicks.filter(timestamp => now - timestamp < 3600000)

    // Check click frequency (max 2 clicks per hour per IP per campaign - more strict)
    if (validClicks.length >= 2) {
      this.blockedIPs.add(ipAddress) // Block this IP
      console.log(`🚫 IP ${ipAddress} blocked for excessive clicks`)
      return {
        isFraudulent: true,
        reason: "Excessive clicks from same IP - IP blocked"
      }
    }

    // Update cache
    validClicks.push(now)
    this.clickFrequencyCache.set(ipKey, validClicks)

    // 2. Rapid clicking detection (max 1 click per 30 seconds - more strict)
    const lastClick = validClicks[validClicks.length - 2]
    if (lastClick && now - lastClick < 30000) {
      this.blockedIPs.add(ipAddress) // Block this IP
      console.log(`🚫 IP ${ipAddress} blocked for rapid clicking`)
      return {
        isFraudulent: true,
        reason: "Rapid clicking detected - IP blocked"
      }
    }

    // 3. Bot detection
    if (!userAgent || this.isBotUserAgent(userAgent)) {
      return {
        isFraudulent: true,
        reason: "Bot or suspicious user agent"
      }
    }

    // 4. Check for click farms (same IP clicking multiple ad spaces rapidly)
    const globalIpKey = `global_${ipAddress}`
    const globalClicks = this.clickFrequencyCache.get(globalIpKey) || []
    const recentGlobalClicks = globalClicks.filter(timestamp => now - timestamp < 600000) // 10 minutes

    if (recentGlobalClicks.length >= 5) { // Reduced from 10 to 5
      this.blockedIPs.add(ipAddress) // Block this IP
      console.log(`🚫 IP ${ipAddress} blocked for click farm behavior`)
      return {
        isFraudulent: true,
        reason: "Click farm behavior detected - IP blocked"
      }
    }

    // Update global cache
    recentGlobalClicks.push(now)
    this.clickFrequencyCache.set(globalIpKey, recentGlobalClicks)

    return { isFraudulent: false }
  }

  /**
   * Check if user agent is a bot
   */
  private isBotUserAgent(userAgent: string): boolean {
    const botPatterns = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /curl/i, /wget/i, /python/i, /java/i,
      /headless/i, /phantom/i, /selenium/i
    ]
    
    return botPatterns.some(pattern => pattern.test(userAgent))
  }

  /**
   * Calculate fair revenue split based on performance and market rates
   */
  private async calculateRevenueSplit(clickData: ClickData): Promise<{
    publisherRevenue: number
    platformRevenue: number
    advertiserCost: number
  }> {
    const { bidAmount, publisherId, advertiserId, adSpaceId } = clickData

    // Get publisher and advertiser pacing settings
    const [publisherPacing, advertiserPacing] = await Promise.all([
      prisma.publisherPacing.findUnique({ where: { publisherId } }),
      prisma.advertiserPacing.findUnique({ where: { advertiserId } })
    ])

    // Base revenue split (70% publisher, 30% platform)
    let publisherShare = 0.70
    let platformShare = 0.30

    // Adjust based on publisher performance
    if (publisherPacing) {
      const publisherPerformance = await this.getPublisherPerformance(publisherId)
      
      // High-performing publishers get better revenue share
      if (publisherPerformance.averageCTR > 3.0) {
        publisherShare = 0.75 // 75% for high CTR
      } else if (publisherPerformance.averageCTR < 1.0) {
        publisherShare = 0.65 // 65% for low CTR
      }
    }

    // Adjust based on ad space quality
    const adSpacePerformance = await this.getAdSpacePerformance(adSpaceId)
    if (adSpacePerformance.qualityScore > 0.8) {
      publisherShare += 0.02 // +2% for high-quality ad spaces
    }

    // Ensure shares add up to 1
    platformShare = 1 - publisherShare

    const publisherRevenue = bidAmount * publisherShare
    const platformRevenue = bidAmount * platformShare

    return {
      publisherRevenue: Math.round(publisherRevenue * 100) / 100,
      platformRevenue: Math.round(platformRevenue * 100) / 100,
      advertiserCost: bidAmount
    }
  }

  /**
   * Record click in database
   */
  private async recordClick(
    clickData: ClickData,
    trackingId: string,
    isFraudulent: boolean,
    fraudReason?: string | null,
    revenueData?: any
  ): Promise<void> {
    const deviceInfo = this.parseDeviceInfo(clickData.userAgent || "")
    const geoLocation = await this.getGeoLocation(clickData.ipAddress)

    await prisma.clickTracking.create({
      data: {
        trackingId,
        campaignId: clickData.campaignId,
        adId: clickData.adId,
        adSpaceId: clickData.adSpaceId,
        publisherId: clickData.publisherId,
        advertiserId: clickData.advertiserId,
        ipAddress: clickData.ipAddress,
        userAgent: clickData.userAgent,
        referrer: clickData.referrer,
        destinationUrl: clickData.destinationUrl,
        isFraudulent,
        fraudReason,
        isValidated: !isFraudulent,
        bidAmount: clickData.bidAmount,
        publisherRevenue: revenueData?.publisherRevenue || 0,
        platformRevenue: revenueData?.platformRevenue || 0,
        deviceType: deviceInfo.deviceType,
        browser: deviceInfo.browser,
        os: deviceInfo.os,
        country: geoLocation.country,
        region: geoLocation.region,
        city: geoLocation.city
      }
    })
  }

  /**
   * Parse device information from user agent
   */
  private parseDeviceInfo(userAgent: string): DeviceInfo {
    const isMobile = /Mobile|Android|iPhone|iPad/i.test(userAgent)
    const isTablet = /iPad|Tablet/i.test(userAgent)
    
    let deviceType = "desktop"
    if (isTablet) deviceType = "tablet"
    else if (isMobile) deviceType = "mobile"

    const browser = this.getBrowser(userAgent)
    const os = this.getOS(userAgent)

    return { deviceType, browser, os }
  }

  private getBrowser(userAgent: string): string {
    if (/Chrome/i.test(userAgent)) return "Chrome"
    if (/Firefox/i.test(userAgent)) return "Firefox"
    if (/Safari/i.test(userAgent)) return "Safari"
    if (/Edge/i.test(userAgent)) return "Edge"
    return "Unknown"
  }

  private getOS(userAgent: string): string {
    if (/Windows/i.test(userAgent)) return "Windows"
    if (/Mac/i.test(userAgent)) return "macOS"
    if (/Linux/i.test(userAgent)) return "Linux"
    if (/Android/i.test(userAgent)) return "Android"
    if (/iOS/i.test(userAgent)) return "iOS"
    return "Unknown"
  }

  /**
   * Get geo location (mock implementation - integrate with real service)
   */
  private async getGeoLocation(ipAddress: string): Promise<GeoLocation> {
    // Mock implementation - in production, use a service like MaxMind or IPinfo
    return {
      country: "India",
      region: "Maharashtra",
      city: "Mumbai"
    }
  }

  /**
   * Get publisher performance metrics
   */
  private async getPublisherPerformance(publisherId: string): Promise<{
    averageCTR: number
    qualityScore: number
  }> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    
    const clicks = await prisma.clickTracking.count({
      where: {
        publisherId,
        createdAt: { gte: thirtyDaysAgo },
        isFraudulent: false
      }
    })

    const impressions = await prisma.adPlacement.aggregate({
      where: {
        adSpace: { publisherId },
        createdAt: { gte: thirtyDaysAgo }
      },
      _sum: { impressions: true }
    })

    const totalImpressions = impressions._sum.impressions || 1
    const averageCTR = (clicks / totalImpressions) * 100

    return {
      averageCTR,
      qualityScore: Math.min(averageCTR / 5, 1) // Quality score based on CTR
    }
  }

  /**
   * Get ad space performance metrics
   */
  private async getAdSpacePerformance(adSpaceId: string): Promise<{
    qualityScore: number
  }> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    
    const validClicks = await prisma.clickTracking.count({
      where: {
        adSpaceId,
        createdAt: { gte: thirtyDaysAgo },
        isFraudulent: false
      }
    })

    const totalClicks = await prisma.clickTracking.count({
      where: {
        adSpaceId,
        createdAt: { gte: thirtyDaysAgo }
      }
    })

    const qualityScore = totalClicks > 0 ? validClicks / totalClicks : 0.8

    return { qualityScore }
  }

  /**
   * Update real-time metrics and deduct budget
   */
  private async updateMetrics(clickData: ClickData, revenueData: any): Promise<void> {
    try {
      // 1. Deduct from advertiser balance
      await prisma.advertiserProfile.update({
        where: { id: clickData.advertiserId },
        data: {
          balance: { decrement: revenueData.advertiserCost }
        }
      })

      // 2. Create wallet transaction for the deduction
      await prisma.walletTransaction.create({
        data: {
          advertiserId: clickData.advertiserId,
          type: "DEBIT",
          amount: revenueData.advertiserCost,
          description: `Click charge for campaign`,
          balanceAfter: 0, // Will be updated by trigger or separate query
          relatedCampaignId: clickData.campaignId
        }
      })

      // 3. Update ad placement metrics
      await prisma.adPlacement.updateMany({
        where: {
          campaignId: clickData.campaignId,
          adId: clickData.adId,
          adSpaceId: clickData.adSpaceId
        },
        data: {
          clicks: { increment: 1 },
          cost: { increment: revenueData.advertiserCost },
          revenue: { increment: revenueData.publisherRevenue }
        }
      })

      // 4. Add revenue to publisher balance
      await prisma.publisherProfile.update({
        where: { id: clickData.publisherId },
        data: {
          balance: { increment: revenueData.publisherRevenue }
        }
      })

      console.log(`💰 Budget updated: Advertiser -₹${revenueData.advertiserCost}, Publisher +₹${revenueData.publisherRevenue}`)

    } catch (error) {
      console.error("Error updating metrics and budget:", error)
      throw error
    }
  }

  /**
   * Trigger pacing adjustments based on click performance
   */
  private async triggerPacingAdjustments(clickData: ClickData): Promise<void> {
    // This will be implemented in the pacing services
    console.log(`Triggering pacing adjustments for campaign ${clickData.campaignId}`)
  }
}
