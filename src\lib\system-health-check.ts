/**
 * System Health Check Utility
 * Validates that all core systems are working properly
 */

import { prisma } from "@/lib/prisma"
import { BiddingEngine } from "@/lib/bidding/bidding-engine"
import { fraudPrevention } from "@/lib/fraud-prevention"

export interface HealthCheckResult {
  component: string
  status: "healthy" | "warning" | "error"
  message: string
  details?: any
}

export interface SystemHealthReport {
  overall: "healthy" | "warning" | "error"
  timestamp: Date
  checks: HealthCheckResult[]
}

export class SystemHealthChecker {
  async runFullHealthCheck(): Promise<SystemHealthReport> {
    const checks: HealthCheckResult[] = []
    
    // Database connectivity check
    checks.push(await this.checkDatabase())
    
    // Bidding engine check
    checks.push(await this.checkBiddingEngine())
    
    // Fraud prevention check
    checks.push(await this.checkFraudPrevention())
    
    // API endpoints check
    checks.push(await this.checkAPIEndpoints())
    
    // Environment variables check
    checks.push(await this.checkEnvironmentVariables())
    
    // Tracking system check
    checks.push(await this.checkTrackingSystem())
    
    // Payment system check
    checks.push(await this.checkPaymentSystem())
    
    // Determine overall health
    const hasErrors = checks.some(check => check.status === "error")
    const hasWarnings = checks.some(check => check.status === "warning")
    
    let overall: "healthy" | "warning" | "error" = "healthy"
    if (hasErrors) {
      overall = "error"
    } else if (hasWarnings) {
      overall = "warning"
    }
    
    return {
      overall,
      timestamp: new Date(),
      checks
    }
  }
  
  private async checkDatabase(): Promise<HealthCheckResult> {
    try {
      // Test basic connectivity
      await prisma.user.findFirst()
      
      // Test that key tables exist and are accessible
      const userCount = await prisma.user.count()
      const campaignCount = await prisma.campaign.count()
      const adSpaceCount = await prisma.adSpace.count()
      
      return {
        component: "Database",
        status: "healthy",
        message: "Database connection successful",
        details: {
          users: userCount,
          campaigns: campaignCount,
          adSpaces: adSpaceCount
        }
      }
    } catch (error) {
      return {
        component: "Database",
        status: "error",
        message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: { error }
      }
    }
  }
  
  private async checkBiddingEngine(): Promise<HealthCheckResult> {
    try {
      const biddingEngine = BiddingEngine.getInstance()
      
      // Test with a mock bid request
      const mockRequest = {
        adSpaceId: "test-space",
        publisherId: "test-publisher",
        publisherRegion: "US",
        publisherCategory: "TECHNOLOGY",
        format: "BANNER" as const,
        width: 300,
        height: 250,
        position: "header",
        userAgent: "test-agent",
        referrer: "test-referrer",
        ipAddress: "127.0.0.1",
        timestamp: new Date()
      }
      
      // This should not throw an error even if no campaigns match
      const result = await biddingEngine.processBidRequest(mockRequest)
      
      return {
        component: "Bidding Engine",
        status: "healthy",
        message: "Bidding engine is operational",
        details: {
          testResult: result.success ? "success" : "no_campaigns",
          error: result.error
        }
      }
    } catch (error) {
      return {
        component: "Bidding Engine",
        status: "error",
        message: `Bidding engine error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: { error }
      }
    }
  }
  
  private async checkFraudPrevention(): Promise<HealthCheckResult> {
    try {
      const stats = fraudPrevention.getStats()
      
      return {
        component: "Fraud Prevention",
        status: "healthy",
        message: "Fraud prevention system is active",
        details: stats
      }
    } catch (error) {
      return {
        component: "Fraud Prevention",
        status: "error",
        message: `Fraud prevention error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: { error }
      }
    }
  }
  
  private async checkAPIEndpoints(): Promise<HealthCheckResult> {
    try {
      // This is a basic check - in a real implementation you might
      // make actual HTTP requests to test endpoints
      const endpoints = [
        "/api/ads/request",
        "/api/tracking/impression",
        "/api/tracking/click",
        "/api/publisher/dashboard",
        "/api/advertiser/dashboard"
      ]
      
      return {
        component: "API Endpoints",
        status: "healthy",
        message: "API endpoints are configured",
        details: {
          endpoints: endpoints.length,
          list: endpoints
        }
      }
    } catch (error) {
      return {
        component: "API Endpoints",
        status: "warning",
        message: "Could not verify all API endpoints",
        details: { error }
      }
    }
  }
  
  private async checkEnvironmentVariables(): Promise<HealthCheckResult> {
    const requiredVars = [
      "DATABASE_URL",
      "NEXTAUTH_SECRET",
      "API_KEY_SECRET",
      "NEXT_PUBLIC_APP_URL"
    ]
    
    const optionalVars = [
      "RAZORPAY_KEY_ID",
      "RAZORPAY_KEY_SECRET"
    ]
    
    const missing = requiredVars.filter(varName => !process.env[varName])
    const missingOptional = optionalVars.filter(varName => !process.env[varName])
    
    if (missing.length > 0) {
      return {
        component: "Environment Variables",
        status: "error",
        message: `Missing required environment variables: ${missing.join(", ")}`,
        details: { missing, missingOptional }
      }
    }
    
    if (missingOptional.length > 0) {
      return {
        component: "Environment Variables",
        status: "warning",
        message: `Missing optional environment variables: ${missingOptional.join(", ")}`,
        details: { missing, missingOptional }
      }
    }
    
    return {
      component: "Environment Variables",
      status: "healthy",
      message: "All required environment variables are set",
      details: { 
        required: requiredVars.length,
        optional: optionalVars.length - missingOptional.length
      }
    }
  }
  
  private async checkTrackingSystem(): Promise<HealthCheckResult> {
    try {
      // Test tracking utility functions
      const { parseTrackingId, generateTrackingId } = await import("@/lib/tracking-utils")
      
      const testId = generateTrackingId("test-campaign", "test-ad", "test-space")
      const parsed = parseTrackingId(testId)
      
      if (!parsed || parsed.campaignId !== "test-campaign") {
        throw new Error("Tracking ID generation/parsing failed")
      }
      
      return {
        component: "Tracking System",
        status: "healthy",
        message: "Tracking system is operational",
        details: {
          testId,
          parsed
        }
      }
    } catch (error) {
      return {
        component: "Tracking System",
        status: "error",
        message: `Tracking system error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: { error }
      }
    }
  }
  
  private async checkPaymentSystem(): Promise<HealthCheckResult> {
    try {
      const hasRazorpayKeys = !!(process.env.RAZORPAY_KEY_ID && process.env.RAZORPAY_KEY_SECRET)
      
      if (!hasRazorpayKeys) {
        return {
          component: "Payment System",
          status: "warning",
          message: "Razorpay credentials not configured - payment system disabled",
          details: { razorpayConfigured: false }
        }
      }
      
      // Test Razorpay configuration
      const { razorpay } = await import("@/lib/razorpay")
      
      return {
        component: "Payment System",
        status: "healthy",
        message: "Payment system is configured",
        details: { 
          razorpayConfigured: true,
          keyId: process.env.RAZORPAY_KEY_ID?.substring(0, 10) + "..."
        }
      }
    } catch (error) {
      return {
        component: "Payment System",
        status: "error",
        message: `Payment system error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: { error }
      }
    }
  }
}

// Export singleton instance
export const systemHealthChecker = new SystemHealthChecker()
