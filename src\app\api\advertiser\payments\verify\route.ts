import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { verifyPaymentSignature, formatAmountFromRazorpay } from "@/lib/razorpay"
import { z } from "zod"

const verifyPaymentSchema = z.object({
  razorpay_order_id: z.string(),
  razorpay_payment_id: z.string(),
  razorpay_signature: z.string(),
  orderId: z.string(), // Our internal order ID
})

// POST /api/advertiser/payments/verify - Verify Razorpay payment
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = verifyPaymentSchema.parse(body)

    // Get advertiser profile
    const advertiserProfile = await prisma.advertiserProfile.findUnique({
      where: { userId: session.user.id },
    })

    if (!advertiserProfile) {
      return NextResponse.json(
        { message: "Advertiser profile not found" },
        { status: 404 }
      )
    }

    // Get payment order
    const paymentOrder = await prisma.paymentOrder.findUnique({
      where: { 
        id: validatedData.orderId,
        advertiserId: advertiserProfile.id,
      },
    })

    if (!paymentOrder) {
      return NextResponse.json(
        { message: "Payment order not found" },
        { status: 404 }
      )
    }

    if (paymentOrder.status === "PAID") {
      return NextResponse.json(
        { message: "Payment already processed" },
        { status: 400 }
      )
    }

    // Verify payment signature
    const isValidSignature = verifyPaymentSignature(
      validatedData.razorpay_order_id,
      validatedData.razorpay_payment_id,
      validatedData.razorpay_signature
    )

    if (!isValidSignature) {
      // Update transaction as failed
      await prisma.paymentTransaction.updateMany({
        where: {
          orderId: paymentOrder.id,
          status: "PENDING",
        },
        data: {
          status: "FAILED",
          failureReason: "Invalid payment signature",
        },
      })

      return NextResponse.json(
        { message: "Invalid payment signature" },
        { status: 400 }
      )
    }

    // Start transaction to update payment status and balance
    const result = await prisma.$transaction(async (tx) => {
      // Update payment order status
      const updatedOrder = await tx.paymentOrder.update({
        where: { id: paymentOrder.id },
        data: { status: "PAID" },
      })

      // Update payment transaction
      await tx.paymentTransaction.updateMany({
        where: {
          orderId: paymentOrder.id,
          status: "PENDING",
        },
        data: {
          razorpayPaymentId: validatedData.razorpay_payment_id,
          razorpaySignature: validatedData.razorpay_signature,
          status: "SUCCESS",
        },
      })

      // Update advertiser balance
      const updatedProfile = await tx.advertiserProfile.update({
        where: { id: advertiserProfile.id },
        data: {
          balance: {
            increment: paymentOrder.amount,
          },
        },
      })

      // Create wallet transaction record
      await tx.walletTransaction.create({
        data: {
          advertiserId: advertiserProfile.id,
          type: "CREDIT",
          amount: paymentOrder.amount,
          description: `Wallet top-up via Razorpay - ${validatedData.razorpay_payment_id}`,
          balanceAfter: updatedProfile.balance,
          relatedOrderId: paymentOrder.id,
        },
      })

      return {
        order: updatedOrder,
        newBalance: updatedProfile.balance,
      }
    })

    return NextResponse.json({
      success: true,
      message: "Payment verified successfully",
      order: result.order,
      newBalance: result.newBalance,
      amountAdded: paymentOrder.amount,
    })
  } catch (error) {
    console.error("Payment verification error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid request data", errors: error.errors },
        { status: 400 }
      )
    }

    // Update transaction as failed if we have the order ID
    try {
      const body = await request.json()
      if (body.orderId) {
        await prisma.paymentTransaction.updateMany({
          where: {
            orderId: body.orderId,
            status: "PENDING",
          },
          data: {
            status: "FAILED",
            failureReason: "Payment verification failed",
          },
        })
      }
    } catch (updateError) {
      console.error("Failed to update transaction status:", updateError)
    }

    return NextResponse.json(
      { message: "Payment verification failed" },
      { status: 500 }
    )
  }
}
