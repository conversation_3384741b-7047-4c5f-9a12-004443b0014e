"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from "next/navigation"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { AdvertiserLayout } from "@/components/dashboard/advertiser-layout"
import { 
  ArrowLeft, 
  Edit, 
  Play, 
  Pause, 
  Plus,
  Eye,
  MousePointer,
  Target,
  Calendar,
  DollarSign,
  Loader2
} from "lucide-react"
import { toast } from "sonner"
import { Campaign, Ad } from "@/lib/types"
import Link from "next/link"

export default function CampaignDetailsPage() {
  const router = useRouter()
  const params = useParams()
  const { data: session } = useSession()
  const [campaign, setCampaign] = useState<Campaign | null>(null)
  const [ads, setAds] = useState<Ad[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)

  useEffect(() => {
    if (params.id) {
      fetchCampaignDetails()
    }
  }, [params.id])

  const fetchCampaignDetails = async () => {
    try {
      setIsLoading(true)
      
      const [campaignResponse, adsResponse] = await Promise.all([
        fetch(`/api/advertiser/campaigns/${params.id}`),
        fetch(`/api/advertiser/ads?campaignId=${params.id}`)
      ])
      
      if (!campaignResponse.ok) {
        if (campaignResponse.status === 404) {
          toast.error("Campaign not found")
          router.push("/dashboard/advertiser/campaigns")
          return
        }
        throw new Error("Failed to fetch campaign")
      }
      
      const campaignData = await campaignResponse.json()
      const adsData = adsResponse.ok ? await adsResponse.json() : { ads: [] }
      
      setCampaign(campaignData.campaign)
      setAds(adsData.ads || [])
    } catch (error) {
      console.error("Failed to fetch campaign details:", error)
      toast.error("Failed to load campaign details")
      router.push("/dashboard/advertiser/campaigns")
    } finally {
      setIsLoading(false)
    }
  }

  const handleStatusChange = async (newStatus: string) => {
    if (!campaign) return

    // Check if campaign can be activated
    if (newStatus === "ACTIVE") {
      const activeAds = ads.filter(ad => ad.isActive)
      if (activeAds.length === 0) {
        toast.error("Cannot activate campaign without active ads. Please create and activate at least one ad first.")
        return
      }
    }

    try {
      setIsUpdatingStatus(true)
      
      const response = await fetch(`/api/advertiser/campaigns/${params.id}/status`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to update campaign status")
      }

      const data = await response.json()
      setCampaign(prev => prev ? { ...prev, status: data.status } : null)
      
      toast.success(`Campaign ${newStatus.toLowerCase()} successfully!`)
    } catch (error) {
      console.error("Failed to update campaign status:", error)
      toast.error(error instanceof Error ? error.message : "Failed to update campaign status")
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-IN").format(num)
  }

  const formatDate = (date: string | Date | null) => {
    if (!date) return "Not set"
    return new Date(date).toLocaleDateString("en-IN", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "bg-green-100 text-green-800"
      case "PAUSED":
        return "bg-yellow-100 text-yellow-800"
      case "DRAFT":
        return "bg-gray-100 text-gray-800"
      case "COMPLETED":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const canActivate = () => {
    if (!campaign) return false
    return campaign.status === "DRAFT" || campaign.status === "PAUSED"
  }

  const canPause = () => {
    if (!campaign) return false
    return campaign.status === "ACTIVE"
  }

  if (isLoading) {
    return (
      <AdvertiserLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </AdvertiserLayout>
    )
  }

  if (!campaign) {
    return (
      <AdvertiserLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-4">Campaign Not Found</h2>
          <Link href="/dashboard/advertiser/campaigns">
            <Button>Back to Campaigns</Button>
          </Link>
        </div>
      </AdvertiserLayout>
    )
  }

  return (
    <AdvertiserLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/dashboard/advertiser/campaigns">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold">{campaign.name}</h1>
              <p className="text-muted-foreground">
                Campaign details and performance
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={getStatusColor(campaign.status)} variant="secondary">
              {campaign.status}
            </Badge>
            {canActivate() && (
              <Button
                onClick={() => handleStatusChange("ACTIVE")}
                disabled={isUpdatingStatus}
                size="sm"
              >
                <Play className="h-4 w-4 mr-2" />
                Activate
              </Button>
            )}
            {canPause() && (
              <Button
                onClick={() => handleStatusChange("PAUSED")}
                disabled={isUpdatingStatus}
                variant="outline"
                size="sm"
              >
                <Pause className="h-4 w-4 mr-2" />
                Pause
              </Button>
            )}
            <Link href={`/dashboard/advertiser/campaigns/${campaign.id}/edit`}>
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </Link>
          </div>
        </div>

        {/* Campaign Status Alert */}
        {campaign.status === "DRAFT" && (
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
                <div>
                  <p className="font-medium text-yellow-800">Campaign is in Draft Mode</p>
                  <p className="text-sm text-yellow-700">
                    {ads.length === 0 
                      ? "Create at least one ad to activate this campaign."
                      : ads.filter(ad => ad.isActive).length === 0
                      ? "Activate at least one ad to make this campaign live."
                      : "Your campaign is ready to go live! Click 'Activate' to start running ads."
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Performance Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Budget Used</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(campaign.spent || 0)}</div>
              <p className="text-xs text-muted-foreground">
                of {formatCurrency(campaign.budget)}
              </p>
              <Progress 
                value={((campaign.spent || 0) / campaign.budget) * 100} 
                className="mt-2"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Impressions</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(campaign.impressions || 0)}</div>
              <p className="text-xs text-muted-foreground">
                Total views
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Clicks</CardTitle>
              <MousePointer className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(campaign.clicks || 0)}</div>
              <p className="text-xs text-muted-foreground">
                {(campaign.ctr || 0).toFixed(2)}% CTR
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Conversions</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{campaign.conversions || 0}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(campaign.costPerConversion || 0)} CPA
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Campaign Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Campaign Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Description</Label>
                    <p className="text-sm text-muted-foreground">
                      {campaign.description || "No description provided"}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Pricing Model</Label>
                    <p className="text-sm text-muted-foreground">{campaign.pricingModel}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Bid Amount</Label>
                    <p className="text-sm text-muted-foreground">{formatCurrency(campaign.bidAmount)}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Daily Budget</Label>
                    <p className="text-sm text-muted-foreground">
                      {campaign.dailyBudget ? formatCurrency(campaign.dailyBudget) : "No limit"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Targeting */}
            <Card>
              <CardHeader>
                <CardTitle>Targeting</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Target Regions</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {campaign.targetRegions?.map((region) => (
                      <Badge key={region} variant="outline">{region}</Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium">Target Categories</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {campaign.targetCategories?.map((category) => (
                      <Badge key={category} variant="outline">{category}</Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Campaign Ads */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Campaign Ads</CardTitle>
                  <CardDescription>
                    Ads running in this campaign
                  </CardDescription>
                </div>
                <Link href={`/dashboard/advertiser/ads?campaignId=${campaign.id}`}>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Ad
                  </Button>
                </Link>
              </CardHeader>
              <CardContent>
                {ads.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground mb-4">No ads created yet</p>
                    <Link href={`/dashboard/advertiser/ads?campaignId=${campaign.id}`}>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Create First Ad
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {ads.map((ad) => (
                      <div key={ad.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-4">
                          <div className="w-16 h-12 bg-gray-100 rounded flex items-center justify-center">
                            {ad.imageUrl ? (
                              <img src={ad.imageUrl} alt={ad.title} className="w-full h-full object-cover rounded" />
                            ) : (
                              <span className="text-xs text-gray-500">{ad.format}</span>
                            )}
                          </div>
                          <div>
                            <h4 className="font-medium">{ad.title}</h4>
                            <p className="text-sm text-muted-foreground">
                              {ad.format} • {ad.width}x{ad.height}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={ad.isActive ? "default" : "secondary"}>
                            {ad.isActive ? "Active" : "Inactive"}
                          </Badge>
                          <div className="text-right text-sm">
                            <p className="font-medium">{formatNumber(ad.impressions || 0)} views</p>
                            <p className="text-muted-foreground">{(ad.ctr || 0).toFixed(2)}% CTR</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Schedule */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Schedule
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-sm font-medium">Start Date</Label>
                  <p className="text-sm text-muted-foreground">{campaign.startDate ? formatDate(campaign.startDate) : "Not set"}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">End Date</Label>
                  <p className="text-sm text-muted-foreground">{campaign.endDate ? formatDate(campaign.endDate) : "Not set"}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Created</Label>
                  <p className="text-sm text-muted-foreground">{formatDate(campaign.createdAt)}</p>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link href={`/dashboard/advertiser/campaigns/${campaign.id}/edit`} className="block">
                  <Button variant="outline" className="w-full">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Campaign
                  </Button>
                </Link>
                <Link href={`/dashboard/advertiser/ads?campaignId=${campaign.id}`} className="block">
                  <Button variant="outline" className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Manage Ads
                  </Button>
                </Link>
                <Link href={`/dashboard/advertiser/analytics?campaignId=${campaign.id}`} className="block">
                  <Button variant="outline" className="w-full">
                    <Target className="h-4 w-4 mr-2" />
                    View Analytics
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AdvertiserLayout>
  )
}

function Label({ children, className }: { children: React.ReactNode; className?: string }) {
  return <label className={className}>{children}</label>
}
