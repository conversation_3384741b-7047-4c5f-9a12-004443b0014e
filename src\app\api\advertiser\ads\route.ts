import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { z } from "zod"
import { AdFormat } from "@prisma/client"

const adCreateSchema = z.object({
  campaignId: z.string().min(1, "Campaign ID is required").refine(
    (id) => /^[0-9a-fA-F]{24}$/.test(id),
    "Invalid campaign ID format"
  ),
  title: z.string().min(1, "Ad title is required"),
  description: z.string().optional(),
  imageUrl: z.string().optional().refine(
    (url) => !url || z.string().url().safeParse(url).success,
    "Invalid image URL"
  ),
  videoUrl: z.string().optional().refine(
    (url) => !url || z.string().url().safeParse(url).success,
    "Invalid video URL"
  ),
  clickUrl: z.string().url("Invalid click URL"),
  format: z.enum(["BANNER", "VIDEO", "NATIVE", "POPUP"]),
  width: z.number().min(1, "Width must be greater than 0"),
  height: z.number().min(1, "Height must be greater than 0"),
})

// GET /api/advertiser/ads - List all ads for the advertiser
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get advertiser profile
    const advertiserProfile = await prisma.advertiserProfile.findUnique({
      where: { userId: session.user.id },
    })

    if (!advertiserProfile) {
      return NextResponse.json(
        { message: "Advertiser profile not found" },
        { status: 404 }
      )
    }

    // Get URL parameters for filtering
    const { searchParams } = new URL(request.url)
    const campaignId = searchParams.get("campaignId")

    // Build where clause
    const whereClause: any = {
      campaign: {
        advertiserId: advertiserProfile.id,
      },
    }

    if (campaignId) {
      whereClause.campaignId = campaignId
    }

    // Get ads with metrics
    const ads = await prisma.ad.findMany({
      where: whereClause,
      include: {
        campaign: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
        adPlacements: {
          select: {
            impressions: true,
            clicks: true,
            conversions: true,
            cost: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    })

    // Calculate metrics for each ad
    const adsWithMetrics = ads.map((ad) => {
      const totalImpressions = ad.adPlacements.reduce((sum, p) => sum + p.impressions, 0)
      const totalClicks = ad.adPlacements.reduce((sum, p) => sum + p.clicks, 0)
      const totalConversions = ad.adPlacements.reduce((sum, p) => sum + p.conversions, 0)
      const totalCost = ad.adPlacements.reduce((sum, p) => sum + p.cost, 0)
      const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0
      const costPerClick = totalClicks > 0 ? totalCost / totalClicks : 0

      return {
        id: ad.id,
        campaignId: ad.campaignId,
        campaignName: ad.campaign.name,
        title: ad.title,
        description: ad.description,
        imageUrl: ad.imageUrl,
        videoUrl: ad.videoUrl,
        clickUrl: ad.clickUrl,
        format: ad.format,
        width: ad.width,
        height: ad.height,
        isActive: ad.isActive,
        createdAt: ad.createdAt,
        updatedAt: ad.updatedAt,
        // Computed metrics
        impressions: totalImpressions,
        clicks: totalClicks,
        conversions: totalConversions,
        ctr: parseFloat(ctr.toFixed(2)),
        costPerClick: parseFloat(costPerClick.toFixed(2)),
      }
    })

    return NextResponse.json({
      ads: adsWithMetrics,
    })
  } catch (error) {
    console.error("Ads list error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/advertiser/ads - Create a new ad
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = adCreateSchema.parse(body)

    // Get advertiser profile
    const advertiserProfile = await prisma.advertiserProfile.findUnique({
      where: { userId: session.user.id },
    })

    if (!advertiserProfile) {
      return NextResponse.json(
        { message: "Advertiser profile not found" },
        { status: 404 }
      )
    }

    // Verify campaign ownership
    const campaign = await prisma.campaign.findUnique({
      where: { id: validatedData.campaignId },
      include: {
        advertiser: true,
      },
    })

    if (!campaign) {
      return NextResponse.json(
        { message: "Campaign not found" },
        { status: 404 }
      )
    }

    if (campaign.advertiser.userId !== session.user.id) {
      return NextResponse.json(
        { message: "You don't own this campaign" },
        { status: 403 }
      )
    }

    // Validate ad format requirements
    if (validatedData.format === "VIDEO" && !validatedData.videoUrl) {
      return NextResponse.json(
        { message: "Video URL is required for video ads" },
        { status: 400 }
      )
    }

    if (validatedData.format !== "VIDEO" && !validatedData.imageUrl) {
      return NextResponse.json(
        { message: "Image URL is required for non-video ads" },
        { status: 400 }
      )
    }

    // Create ad
    const ad = await prisma.ad.create({
      data: {
        campaignId: validatedData.campaignId,
        title: validatedData.title,
        description: validatedData.description,
        imageUrl: validatedData.imageUrl,
        videoUrl: validatedData.videoUrl,
        clickUrl: validatedData.clickUrl,
        format: validatedData.format as AdFormat,
        width: validatedData.width,
        height: validatedData.height,
        isActive: true,
      },
      include: {
        campaign: {
          select: {
            name: true,
          },
        },
      },
    })

    return NextResponse.json(
      {
        message: "Ad created successfully",
        ad: {
          id: ad.id,
          title: ad.title,
          campaignName: ad.campaign.name,
          format: ad.format,
          isActive: ad.isActive,
        },
      },
      { status: 201 }
    )
  } catch (error) {
    console.error("Ad creation error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid request data", errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
