/**
 * Campaign Optimizer
 * Background job system for optimizing campaign performance and matching
 */

import { prisma } from "@/lib/prisma"
import { AdvertiserPacingService } from "@/lib/pacing/advertiser-pacing"
import { PublisherPacingService } from "@/lib/pacing/publisher-pacing"
import { PricingModel, CampaignStatus } from "@prisma/client"

interface CampaignMetrics {
  campaignId: string
  impressions: number
  clicks: number
  conversions: number
  cost: number
  revenue: number
  ctr: number
  conversionRate: number
  costPerClick: number
  costPerConversion: number
  roi: number
}

interface OptimizationRecommendation {
  campaignId: string
  type: "BID_ADJUSTMENT" | "BUDGET_REALLOCATION" | "TARGETING_OPTIMIZATION" | "PAUSE_CAMPAIGN"
  reason: string
  suggestedAction: string
  priority: "HIGH" | "MEDIUM" | "LOW"
  estimatedImpact: string
}

export class CampaignOptimizer {
  private static instance: CampaignOptimizer
  private isRunning = false
  private optimizationInterval: NodeJS.Timeout | null = null

  static getInstance(): CampaignOptimizer {
    if (!CampaignOptimizer.instance) {
      CampaignOptimizer.instance = new CampaignOptimizer()
    }
    return CampaignOptimizer.instance
  }

  // Start the optimization process
  start(intervalMs: number = 5 * 60 * 1000): void { // Default: 5 minutes
    if (this.isRunning) {
      console.log("Campaign optimizer is already running")
      return
    }

    this.isRunning = true
    console.log("Starting campaign optimizer...")

    // Run immediately
    this.runOptimization()

    // Set up recurring optimization
    this.optimizationInterval = setInterval(() => {
      this.runOptimization()
    }, intervalMs)
  }

  // Stop the optimization process
  stop(): void {
    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval)
      this.optimizationInterval = null
    }
    this.isRunning = false
    console.log("Campaign optimizer stopped")
  }

  // Main optimization routine
  private async runOptimization(): Promise<void> {
    try {
      console.log("Running campaign optimization...")

      // 1. Update expired campaigns
      await this.updateExpiredCampaigns()

      // 2. Update campaign metrics
      await this.updateCampaignMetrics()

      // 3. Check budget exhaustion
      await this.checkBudgetExhaustion()

      // 4. Optimize bid amounts
      await this.optimizeBidAmounts()

      // 5. Reallocate budgets
      await this.reallocateBudgets()

      // 6. Update targeting based on performance
      await this.optimizeTargeting()

      // 7. Pause underperforming campaigns
      await this.pauseUnderperformingCampaigns()

      // 8. Run advertiser pacing optimization
      const advertiserPacing = AdvertiserPacingService.getInstance()
      await advertiserPacing.runPacingOptimization()

      // 9. Run publisher pacing optimization
      const publisherPacing = PublisherPacingService.getInstance()
      await publisherPacing.runPacingOptimization()

      console.log("Campaign optimization completed")

    } catch (error) {
      console.error("Campaign optimization error:", error)
    }
  }

  // Update campaigns that have passed their end date
  private async updateExpiredCampaigns(): Promise<void> {
    try {
      const now = new Date()

      // Find active campaigns that have passed their end date
      const expiredCampaigns = await prisma.campaign.findMany({
        where: {
          status: "ACTIVE",
          endDate: {
            lt: now
          }
        },
        select: {
          id: true,
          name: true,
          endDate: true,
          advertiser: {
            select: {
              id: true,
              companyName: true
            }
          }
        }
      })

      // Update expired campaigns to PAUSED status (so they can be reactivated if end date is extended)
      for (const campaign of expiredCampaigns) {
        await prisma.campaign.update({
          where: { id: campaign.id },
          data: {
            status: "PAUSED",
            updatedAt: now
          }
        })

        console.log(`Campaign ${campaign.id} (${campaign.name}) automatically paused due to end date: ${campaign.endDate}`)

        // Log the pause in wallet transactions for audit trail
        await prisma.walletTransaction.create({
          data: {
            advertiserId: campaign.advertiser.id,
            type: "CREDIT", // No actual credit, just logging
            amount: 0,
            description: `Campaign "${campaign.name}" automatically paused (end date reached)`,
            balanceAfter: 0, // Will be updated by the actual balance
            relatedCampaignId: campaign.id,
          },
        })
      }

      if (expiredCampaigns.length > 0) {
        console.log(`Updated ${expiredCampaigns.length} expired campaigns to PAUSED status`)
      }

    } catch (error) {
      console.error("Error updating expired campaigns:", error)
    }
  }

  // Update real-time metrics for all active campaigns
  private async updateCampaignMetrics(): Promise<void> {
    try {
      const activeCampaigns = await prisma.campaign.findMany({
        where: { status: "ACTIVE" },
        include: {
          adPlacements: {
            select: {
              impressions: true,
              clicks: true,
              conversions: true,
              cost: true,
              revenue: true
            }
          }
        }
      })

      for (const campaign of activeCampaigns) {
        const metrics = this.calculateCampaignMetrics(campaign)
        
        // Store metrics in cache or database for quick access
        // In a real implementation, you might use Redis or a dedicated metrics table
        console.log(`Campaign ${campaign.id} metrics:`, {
          impressions: metrics.impressions,
          clicks: metrics.clicks,
          ctr: metrics.ctr,
          cost: metrics.cost,
          roi: metrics.roi
        })
      }

    } catch (error) {
      console.error("Error updating campaign metrics:", error)
    }
  }

  // Check for campaigns that have exhausted their budgets
  private async checkBudgetExhaustion(): Promise<void> {
    try {
      const campaigns = await prisma.campaign.findMany({
        where: { 
          status: "ACTIVE",
          budget: { gt: 0 }
        },
        include: {
          adPlacements: {
            select: { cost: true }
          }
        }
      })

      for (const campaign of campaigns) {
        const totalSpent = campaign.adPlacements.reduce((sum, p) => sum + p.cost, 0)
        
        if (totalSpent >= campaign.budget) {
          await prisma.campaign.update({
            where: { id: campaign.id },
            data: { 
              status: "COMPLETED",
              endDate: new Date()
            }
          })
          
          console.log(`Campaign ${campaign.id} completed due to budget exhaustion`)
          
          // Send notification to advertiser
          await this.sendBudgetExhaustionNotification(campaign.id)
        }
      }

    } catch (error) {
      console.error("Error checking budget exhaustion:", error)
    }
  }

  // Optimize bid amounts based on performance
  private async optimizeBidAmounts(): Promise<void> {
    try {
      const campaigns = await prisma.campaign.findMany({
        where: { status: "ACTIVE" },
        include: {
          adPlacements: {
            where: {
              createdAt: {
                gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
              }
            }
          }
        }
      })

      for (const campaign of campaigns) {
        const metrics = this.calculateCampaignMetrics(campaign)
        
        // Adjust bid based on performance
        let bidAdjustment = 0
        
        if (metrics.ctr > 2.0 && metrics.conversionRate > 5.0) {
          // High performance - increase bid by 10%
          bidAdjustment = 0.1
        } else if (metrics.ctr < 0.5 || metrics.conversionRate < 1.0) {
          // Low performance - decrease bid by 15%
          bidAdjustment = -0.15
        }

        if (bidAdjustment !== 0) {
          const newBidAmount = Math.max(0.01, campaign.bidAmount * (1 + bidAdjustment))
          
          await prisma.campaign.update({
            where: { id: campaign.id },
            data: { bidAmount: newBidAmount }
          })
          
          console.log(`Adjusted bid for campaign ${campaign.id}: ${campaign.bidAmount} -> ${newBidAmount}`)
        }
      }

    } catch (error) {
      console.error("Error optimizing bid amounts:", error)
    }
  }

  // Reallocate budgets based on performance
  private async reallocateBudgets(): Promise<void> {
    try {
      // Get all active campaigns for each advertiser
      const advertisers = await prisma.advertiserProfile.findMany({
        include: {
          campaigns: {
            where: { status: "ACTIVE" },
            include: {
              adPlacements: true
            }
          }
        }
      })

      for (const advertiser of advertisers) {
        if (advertiser.campaigns.length <= 1) continue

        // Calculate performance scores for each campaign
        const campaignPerformance = advertiser.campaigns.map(campaign => {
          const metrics = this.calculateCampaignMetrics(campaign)
          return {
            campaignId: campaign.id,
            currentBudget: campaign.budget,
            roi: metrics.roi,
            score: metrics.roi * metrics.ctr * metrics.conversionRate
          }
        })

        // Reallocate budget to better performing campaigns
        // This is a simplified example - in reality, you'd want more sophisticated logic
        const totalBudget = campaignPerformance.reduce((sum, c) => sum + c.currentBudget, 0)
        const totalScore = campaignPerformance.reduce((sum, c) => sum + Math.max(0.1, c.score), 0)

        for (const campaign of campaignPerformance) {
          const budgetShare = Math.max(0.1, campaign.score) / totalScore
          const newBudget = totalBudget * budgetShare
          
          if (Math.abs(newBudget - campaign.currentBudget) > campaign.currentBudget * 0.1) {
            await prisma.campaign.update({
              where: { id: campaign.campaignId },
              data: { budget: newBudget }
            })
            
            console.log(`Reallocated budget for campaign ${campaign.campaignId}: ${campaign.currentBudget} -> ${newBudget}`)
          }
        }
      }

    } catch (error) {
      console.error("Error reallocating budgets:", error)
    }
  }

  // Optimize targeting based on performance data
  private async optimizeTargeting(): Promise<void> {
    // This would analyze which regions/categories perform best
    // and suggest targeting adjustments
    console.log("Targeting optimization - placeholder for future implementation")
  }

  // Pause campaigns with consistently poor performance
  private async pauseUnderperformingCampaigns(): Promise<void> {
    try {
      const campaigns = await prisma.campaign.findMany({
        where: { 
          status: "ACTIVE",
          createdAt: {
            lte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // At least 7 days old
          }
        },
        include: {
          adPlacements: true
        }
      })

      for (const campaign of campaigns) {
        const metrics = this.calculateCampaignMetrics(campaign)
        
        // Pause if very poor performance
        if (metrics.impressions > 1000 && (metrics.ctr < 0.1 || metrics.roi < -0.5)) {
          await prisma.campaign.update({
            where: { id: campaign.id },
            data: { status: "PAUSED" }
          })
          
          console.log(`Paused underperforming campaign ${campaign.id}`)
          await this.sendUnderperformanceNotification(campaign.id)
        }
      }

    } catch (error) {
      console.error("Error pausing underperforming campaigns:", error)
    }
  }

  // Calculate comprehensive metrics for a campaign
  private calculateCampaignMetrics(campaign: any): CampaignMetrics {
    const impressions = campaign.adPlacements.reduce((sum: number, p: any) => sum + p.impressions, 0)
    const clicks = campaign.adPlacements.reduce((sum: number, p: any) => sum + p.clicks, 0)
    const conversions = campaign.adPlacements.reduce((sum: number, p: any) => sum + p.conversions, 0)
    const cost = campaign.adPlacements.reduce((sum: number, p: any) => sum + p.cost, 0)
    const revenue = campaign.adPlacements.reduce((sum: number, p: any) => sum + p.revenue, 0)

    const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0
    const conversionRate = clicks > 0 ? (conversions / clicks) * 100 : 0
    const costPerClick = clicks > 0 ? cost / clicks : 0
    const costPerConversion = conversions > 0 ? cost / conversions : 0
    const roi = cost > 0 ? ((revenue - cost) / cost) * 100 : 0

    return {
      campaignId: campaign.id,
      impressions,
      clicks,
      conversions,
      cost,
      revenue,
      ctr,
      conversionRate,
      costPerClick,
      costPerConversion,
      roi
    }
  }

  // Send notification when budget is exhausted
  private async sendBudgetExhaustionNotification(campaignId: string): Promise<void> {
    // In a real implementation, send email/SMS/push notification
    console.log(`Budget exhaustion notification sent for campaign ${campaignId}`)
  }

  // Send notification for underperforming campaigns
  private async sendUnderperformanceNotification(campaignId: string): Promise<void> {
    // In a real implementation, send email/SMS/push notification
    console.log(`Underperformance notification sent for campaign ${campaignId}`)
  }
}

// Export singleton instance
export const campaignOptimizer = CampaignOptimizer.getInstance()
