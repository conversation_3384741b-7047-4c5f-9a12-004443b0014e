import { prisma } from "@/lib/prisma"
import crypto from "crypto"

interface FrequencyCheckRequest {
  campaignId: string
  adId: string
  adSpaceId: string
  userId?: string
  ipAddress: string
  userAgent: string
  deviceType?: string
}

interface FrequencyCheckResult {
  allowed: boolean
  currentFrequency: number
  frequencyCap: number
  reason?: string
  timeUntilReset?: number
}

export class FrequencyManager {
  private static instance: FrequencyManager

  static getInstance(): FrequencyManager {
    if (!FrequencyManager.instance) {
      FrequencyManager.instance = new FrequencyManager()
    }
    return FrequencyManager.instance
  }

  /**
   * Generate browser fingerprint for anonymous users
   */
  private generateFingerprint(ipAddress: string, userAgent: string): string {
    const data = `${ipAddress}_${userAgent}`
    return crypto.createHash('sha256').update(data).digest('hex').substring(0, 16)
  }

  /**
   * Get period boundaries based on frequency period type
   */
  private getPeriodBoundaries(period: string): { start: Date; end: Date } {
    const now = new Date()
    
    switch (period) {
      case "DAILY":
        const dayStart = new Date(now)
        dayStart.setHours(0, 0, 0, 0)
        const dayEnd = new Date(dayStart)
        dayEnd.setDate(dayEnd.getDate() + 1)
        return { start: dayStart, end: dayEnd }
        
      case "WEEKLY":
        const weekStart = new Date(now)
        weekStart.setDate(now.getDate() - now.getDay()) // Start of week (Sunday)
        weekStart.setHours(0, 0, 0, 0)
        const weekEnd = new Date(weekStart)
        weekEnd.setDate(weekEnd.getDate() + 7)
        return { start: weekStart, end: weekEnd }
        
      case "CAMPAIGN":
        // For campaign period, we'll use a 30-day window
        const campaignStart = new Date(now)
        campaignStart.setDate(now.getDate() - 30)
        const campaignEnd = new Date(now)
        campaignEnd.setDate(now.getDate() + 30)
        return { start: campaignStart, end: campaignEnd }
        
      default:
        return this.getPeriodBoundaries("DAILY")
    }
  }

  /**
   * Check if user has exceeded frequency cap for a campaign
   */
  async checkFrequency(request: FrequencyCheckRequest): Promise<FrequencyCheckResult> {
    try {
      // Get campaign frequency settings
      const campaign = await prisma.campaign.findUnique({
        where: { id: request.campaignId },
        select: {
          frequencyCap: true,
          frequencyPeriod: true,
          name: true
        }
      })

      if (!campaign) {
        return {
          allowed: false,
          currentFrequency: 0,
          frequencyCap: 0,
          reason: "Campaign not found"
        }
      }

      // Generate user identifier
      const fingerprint = request.userId || this.generateFingerprint(request.ipAddress, request.userAgent)
      
      // Get period boundaries
      const { start: periodStart, end: periodEnd } = this.getPeriodBoundaries(campaign.frequencyPeriod)

      // Check existing frequency tracking
      const existingTracking = await prisma.frequencyTracking.findFirst({
        where: {
          ...(request.userId ? { userId: request.userId } : { fingerprint }),
          campaignId: request.campaignId,
          periodStart: { lte: periodStart },
          periodEnd: { gte: periodEnd }
        }
      })

      const currentFrequency = existingTracking?.impressions || 0

      // Check if frequency cap is exceeded
      if (currentFrequency >= campaign.frequencyCap) {
        const timeUntilReset = periodEnd.getTime() - Date.now()
        
        return {
          allowed: false,
          currentFrequency,
          frequencyCap: campaign.frequencyCap,
          reason: `Frequency cap exceeded (${currentFrequency}/${campaign.frequencyCap})`,
          timeUntilReset: Math.max(0, timeUntilReset)
        }
      }

      return {
        allowed: true,
        currentFrequency,
        frequencyCap: campaign.frequencyCap
      }

    } catch (error) {
      console.error("Frequency check error:", error)
      return {
        allowed: true, // Fail open to not block ads
        currentFrequency: 0,
        frequencyCap: 0,
        reason: "Frequency check failed"
      }
    }
  }

  /**
   * Record an impression for frequency tracking
   */
  async recordImpression(request: FrequencyCheckRequest): Promise<void> {
    try {
      const campaign = await prisma.campaign.findUnique({
        where: { id: request.campaignId },
        select: { frequencyPeriod: true }
      })

      if (!campaign) return

      const fingerprint = request.userId || this.generateFingerprint(request.ipAddress, request.userAgent)
      const { start: periodStart, end: periodEnd } = this.getPeriodBoundaries(campaign.frequencyPeriod)

      // Try to update existing tracking record
      const updated = await prisma.frequencyTracking.updateMany({
        where: {
          ...(request.userId ? { userId: request.userId } : { fingerprint }),
          campaignId: request.campaignId,
          periodStart: { lte: periodStart },
          periodEnd: { gte: periodEnd }
        },
        data: {
          impressions: { increment: 1 },
          lastSeen: new Date(),
          adId: request.adId,
          adSpaceId: request.adSpaceId,
          ipAddress: request.ipAddress,
          userAgent: request.userAgent,
          deviceType: request.deviceType
        }
      })

      // If no existing record, create new one
      if (updated.count === 0) {
        await prisma.frequencyTracking.create({
          data: {
            userId: request.userId,
            fingerprint,
            campaignId: request.campaignId,
            adId: request.adId,
            adSpaceId: request.adSpaceId,
            impressions: 1,
            period: campaign.frequencyPeriod,
            periodStart,
            periodEnd,
            ipAddress: request.ipAddress,
            userAgent: request.userAgent,
            deviceType: request.deviceType
          }
        })
      }

      console.log(`📊 Frequency recorded: Campaign ${request.campaignId}, User ${fingerprint.substring(0, 8)}...`)

    } catch (error) {
      console.error("Error recording frequency:", error)
    }
  }

  /**
   * Get frequency analytics for a campaign
   */
  async getCampaignFrequencyAnalytics(campaignId: string): Promise<{
    totalUsers: number
    averageFrequency: number
    frequencyDistribution: { frequency: number; users: number }[]
    cappedUsers: number
    topFrequencyUsers: { fingerprint: string; impressions: number }[]
  }> {
    try {
      const campaign = await prisma.campaign.findUnique({
        where: { id: campaignId },
        select: { frequencyCap: true, frequencyPeriod: true }
      })

      if (!campaign) {
        return {
          totalUsers: 0,
          averageFrequency: 0,
          frequencyDistribution: [],
          cappedUsers: 0,
          topFrequencyUsers: []
        }
      }

      const { start: periodStart, end: periodEnd } = this.getPeriodBoundaries(campaign.frequencyPeriod)

      // Get all frequency tracking records for this period
      const trackingRecords = await prisma.frequencyTracking.findMany({
        where: {
          campaignId,
          periodStart: { lte: periodStart },
          periodEnd: { gte: periodEnd }
        },
        select: {
          fingerprint: true,
          userId: true,
          impressions: true
        }
      })

      const totalUsers = trackingRecords.length
      const totalImpressions = trackingRecords.reduce((sum, record) => sum + record.impressions, 0)
      const averageFrequency = totalUsers > 0 ? totalImpressions / totalUsers : 0

      // Calculate frequency distribution
      const frequencyMap = new Map<number, number>()
      trackingRecords.forEach(record => {
        const freq = record.impressions
        frequencyMap.set(freq, (frequencyMap.get(freq) || 0) + 1)
      })

      const frequencyDistribution = Array.from(frequencyMap.entries())
        .map(([frequency, users]) => ({ frequency, users }))
        .sort((a, b) => a.frequency - b.frequency)

      // Count users who hit frequency cap
      const cappedUsers = trackingRecords.filter(record => 
        record.impressions >= campaign.frequencyCap
      ).length

      // Get top frequency users
      const topFrequencyUsers = trackingRecords
        .sort((a, b) => b.impressions - a.impressions)
        .slice(0, 10)
        .map(record => ({
          fingerprint: (record.userId || record.fingerprint).substring(0, 8) + "...",
          impressions: record.impressions
        }))

      return {
        totalUsers,
        averageFrequency: Math.round(averageFrequency * 100) / 100,
        frequencyDistribution,
        cappedUsers,
        topFrequencyUsers
      }

    } catch (error) {
      console.error("Error getting frequency analytics:", error)
      return {
        totalUsers: 0,
        averageFrequency: 0,
        frequencyDistribution: [],
        cappedUsers: 0,
        topFrequencyUsers: []
      }
    }
  }

  /**
   * Clean up old frequency tracking records
   */
  async cleanupOldRecords(): Promise<void> {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      
      const deleted = await prisma.frequencyTracking.deleteMany({
        where: {
          periodEnd: { lt: thirtyDaysAgo }
        }
      })

      if (deleted.count > 0) {
        console.log(`🧹 Cleaned up ${deleted.count} old frequency tracking records`)
      }
    } catch (error) {
      console.error("Error cleaning up frequency records:", error)
    }
  }

  /**
   * Get user's frequency status across all campaigns
   */
  async getUserFrequencyStatus(userId?: string, fingerprint?: string): Promise<{
    activeCampaigns: number
    totalImpressions: number
    cappedCampaigns: number
    campaigns: Array<{
      campaignId: string
      campaignName: string
      impressions: number
      frequencyCap: number
      isCapped: boolean
    }>
  }> {
    try {
      const identifier = userId ? { userId } : { fingerprint }
      
      const trackingRecords = await prisma.frequencyTracking.findMany({
        where: {
          ...identifier,
          periodEnd: { gte: new Date() }
        },
        include: {
          campaign: {
            select: {
              name: true,
              frequencyCap: true
            }
          }
        }
      })

      const totalImpressions = trackingRecords.reduce((sum, record) => sum + record.impressions, 0)
      const cappedCampaigns = trackingRecords.filter(record => 
        record.impressions >= record.campaign.frequencyCap
      ).length

      const campaigns = trackingRecords.map(record => ({
        campaignId: record.campaignId,
        campaignName: record.campaign.name,
        impressions: record.impressions,
        frequencyCap: record.campaign.frequencyCap,
        isCapped: record.impressions >= record.campaign.frequencyCap
      }))

      return {
        activeCampaigns: trackingRecords.length,
        totalImpressions,
        cappedCampaigns,
        campaigns
      }

    } catch (error) {
      console.error("Error getting user frequency status:", error)
      return {
        activeCampaigns: 0,
        totalImpressions: 0,
        cappedCampaigns: 0,
        campaigns: []
      }
    }
  }
}
