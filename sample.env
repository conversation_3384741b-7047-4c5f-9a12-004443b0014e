# Database
DATABASE_URL="mongodb+srv://adnetwork:<EMAIL>/adnetwork"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-change-in-production"

# API Keys Encryption
API_KEY_SECRET="your-api-key-encryption-secret-here"

# App Configuration
APP_NAME="Ad Network Platform"
APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Razorpay Configuration
RAZORPAY_KEY_ID="rzp_test_ULeG436WhV4UyP"
RAZORPAY_KEY_SECRET="oXDBzLPs2LJZUDHreKqhzD8c"


# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDYgPziAoaKCgX9vxZJOKyQXtTh1HYdGMQ
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=ad-network-3c84a.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=ad-network-3c84a
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=ad-network-3c84a.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=112128264802880891788
NEXT_PUBLIC_FIREBASE_APP_ID=1:112128264802880891788:web:a493671b65604aa2aba766
# You'll need to generate a VAPID key in the Firebase console
NEXT_PUBLIC_FIREBASE_VAPID_KEY=BNzriPwxuBnw9tDZwYIAgk0aBqr5fJGatpA5rQCLFZgbFqGjV2W2zlrc-pCgr-OOlSKKKusWRRiudtSV0-t9gUk

# Firebase Admin Configuration (Server-side)
FIREBASE_PROJECT_ID=ad-network-3c84a
FIREBASE_CLIENT_EMAIL=<EMAIL>
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_DATABASE_URL=https://ad-network-3c84a.firebaseio.com
