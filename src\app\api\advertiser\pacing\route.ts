import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { AdvertiserPacingService } from "@/lib/pacing/advertiser-pacing"

// GET /api/advertiser/pacing - Get pacing settings and insights
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get advertiser profile
    const advertiserProfile = await prisma.advertiserProfile.findUnique({
      where: { userId: session.user.id }
    })

    if (!advertiserProfile) {
      return NextResponse.json(
        { message: "Advertiser profile not found" },
        { status: 404 }
      )
    }

    // Initialize pacing if not exists
    const pacingService = AdvertiserPacingService.getInstance()
    await pacingService.initializePacing(advertiserProfile.id)

    // Get pacing insights
    const insights = await pacingService.getPacingInsights(advertiserProfile.id)

    return NextResponse.json({
      success: true,
      data: insights
    })

  } catch (error) {
    console.error("Error fetching advertiser pacing:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/advertiser/pacing - Update pacing settings
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      dailyBudgetPacing,
      weeklyBudgetPacing,
      monthlyBudgetPacing,
      bidPacingStrategy,
      maxBidIncrease,
      maxBidDecrease,
      targetCTR,
      targetConversionRate,
      targetCPA,
      targetROAS
    } = body

    // Get advertiser profile
    const advertiserProfile = await prisma.advertiserProfile.findUnique({
      where: { userId: session.user.id }
    })

    if (!advertiserProfile) {
      return NextResponse.json(
        { message: "Advertiser profile not found" },
        { status: 404 }
      )
    }

    // Validate input values
    if (maxBidIncrease && (maxBidIncrease < 0 || maxBidIncrease > 2)) {
      return NextResponse.json(
        { message: "Max bid increase must be between 0 and 2 (200%)" },
        { status: 400 }
      )
    }

    if (maxBidDecrease && (maxBidDecrease < 0 || maxBidDecrease > 1)) {
      return NextResponse.json(
        { message: "Max bid decrease must be between 0 and 1 (100%)" },
        { status: 400 }
      )
    }

    if (targetCTR && (targetCTR < 0 || targetCTR > 50)) {
      return NextResponse.json(
        { message: "Target CTR must be between 0 and 50%" },
        { status: 400 }
      )
    }

    // Update pacing settings
    const updatedPacing = await prisma.advertiserPacing.upsert({
      where: { advertiserId: advertiserProfile.id },
      update: {
        dailyBudgetPacing: dailyBudgetPacing || undefined,
        weeklyBudgetPacing: weeklyBudgetPacing || undefined,
        monthlyBudgetPacing: monthlyBudgetPacing || undefined,
        bidPacingStrategy: bidPacingStrategy || undefined,
        maxBidIncrease: maxBidIncrease !== undefined ? maxBidIncrease : undefined,
        maxBidDecrease: maxBidDecrease !== undefined ? maxBidDecrease : undefined,
        targetCTR: targetCTR !== undefined ? targetCTR : undefined,
        targetConversionRate: targetConversionRate !== undefined ? targetConversionRate : undefined,
        targetCPA: targetCPA !== undefined ? targetCPA : undefined,
        targetROAS: targetROAS !== undefined ? targetROAS : undefined,
        updatedAt: new Date()
      },
      create: {
        advertiserId: advertiserProfile.id,
        dailyBudgetPacing: dailyBudgetPacing || "EVEN",
        weeklyBudgetPacing: weeklyBudgetPacing || "EVEN",
        monthlyBudgetPacing: monthlyBudgetPacing || "EVEN",
        bidPacingStrategy: bidPacingStrategy || "PERFORMANCE_BASED",
        maxBidIncrease: maxBidIncrease || 0.5,
        maxBidDecrease: maxBidDecrease || 0.3,
        targetCTR: targetCTR || 2.0,
        targetConversionRate: targetConversionRate || 5.0,
        targetCPA,
        targetROAS
      }
    })

    return NextResponse.json({
      success: true,
      message: "Pacing settings updated successfully",
      data: updatedPacing
    })

  } catch (error) {
    console.error("Error updating advertiser pacing:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/advertiser/pacing/optimize - Trigger manual optimization
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get advertiser profile
    const advertiserProfile = await prisma.advertiserProfile.findUnique({
      where: { userId: session.user.id }
    })

    if (!advertiserProfile) {
      return NextResponse.json(
        { message: "Advertiser profile not found" },
        { status: 404 }
      )
    }

    // Run optimization for this specific advertiser
    const pacingService = AdvertiserPacingService.getInstance()
    await pacingService.runPacingOptimization()

    // Get updated insights
    const insights = await pacingService.getPacingInsights(advertiserProfile.id)

    return NextResponse.json({
      success: true,
      message: "Pacing optimization completed",
      data: insights
    })

  } catch (error) {
    console.error("Error running advertiser pacing optimization:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
