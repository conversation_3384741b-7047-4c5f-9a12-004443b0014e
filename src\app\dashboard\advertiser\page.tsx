"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { AdvertiserLayout } from "@/components/dashboard/advertiser-layout"
import {
  DollarSign,
  Eye,
  MousePointer,
  TrendingUp,
  Megaphone,
  Target,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  PlusCircle,
  Plus,
  Play,
  Pause,
  BarChart3
} from "lucide-react"
import { toast } from "sonner"
import { AdvertiserStats, Campaign } from "@/lib/types"

export default function AdvertiserDashboardPage() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<AdvertiserStats>({
    totalSpent: 0,
    todaySpent: 0,
    totalImpressions: 0,
    todayImpressions: 0,
    totalClicks: 0,
    todayClicks: 0,
    ctr: 0,
    activeCampaigns: 0,
    remainingBudget: 0,
    conversions: 0,
    costPerConversion: 0,
  })
  const [recentCampaigns, setRecentCampaigns] = useState<Campaign[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true)

      // Fetch dashboard data from API
      const response = await fetch("/api/advertiser/dashboard")

      if (!response.ok) {
        throw new Error("Failed to fetch dashboard data")
      }

      const data = await response.json()

      setStats(data.stats)
      setRecentCampaigns(data.recentCampaigns || [])
    } catch (error) {
      console.error("Failed to fetch dashboard data:", error)
      toast.error("Failed to load dashboard data")
    } finally {
      setIsLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US").format(num)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <Badge className="bg-green-100 text-green-800"><Play className="w-3 h-3 mr-1" />Active</Badge>
      case "PAUSED":
        return <Badge className="bg-yellow-100 text-yellow-800"><Pause className="w-3 h-3 mr-1" />Paused</Badge>
      case "DRAFT":
        return <Badge variant="secondary">Draft</Badge>
      case "COMPLETED":
        return <Badge className="bg-blue-100 text-blue-800">Completed</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  if (!session || session.user.role !== "ADVERTISER") {
    return <div>Access denied</div>
  }

  return (
    <AdvertiserLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Campaign Overview</h2>
            <p className="text-muted-foreground">
              Monitor your advertising performance and manage campaigns
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              {new Date().toLocaleDateString("en-US", { 
                weekday: "long", 
                year: "numeric", 
                month: "long", 
                day: "numeric" 
              })}
            </span>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.totalSpent)}</div>
              <p className="text-xs text-muted-foreground">
                Today: {formatCurrency(stats.todaySpent)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Impressions</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(stats.totalImpressions)}</div>
              <p className="text-xs text-muted-foreground">
                Today: {formatNumber(stats.todayImpressions)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Clicks</CardTitle>
              <MousePointer className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(stats.totalClicks)}</div>
              <p className="text-xs text-muted-foreground">
                Today: {formatNumber(stats.todayClicks)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">CTR</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.ctr}%</div>
              <p className="text-xs text-muted-foreground">
                Click-through rate
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions & Status */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Megaphone className="h-5 w-5" />
                Active Campaigns
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-2">{stats.activeCampaigns}</div>
              <p className="text-sm text-muted-foreground mb-4">Currently running</p>
              <Link href="/dashboard/advertiser/campaigns">
                <Button className="w-full" size="sm">
                  Manage Campaigns
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Remaining Budget
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-2">{formatCurrency(stats.remainingBudget)}</div>
              <p className="text-sm text-muted-foreground mb-4">Available to spend</p>
              <Link href="/dashboard/advertiser/billing">
                <Button variant="outline" className="w-full" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Funds
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Budget Utilization</span>
                  <span>63%</span>
                </div>
                <Progress value={63} className="h-2" />
                <p className="text-xs text-muted-foreground">
                  {formatCurrency(stats.totalSpent)} of {formatCurrency(stats.totalSpent + stats.remainingBudget)} total budget
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Campaigns */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Recent Campaigns</CardTitle>
                <CardDescription>
                  Your latest advertising campaigns and their performance
                </CardDescription>
              </div>
              <Link href="/dashboard/advertiser/campaigns/create">
                <Button>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Create Campaign
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentCampaigns.length > 0 ? (
                recentCampaigns.map((campaign) => (
                  <div key={campaign.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div>
                        <h4 className="font-medium">{campaign.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          Budget: {formatCurrency(campaign.budget)} • Spent: {formatCurrency(campaign.spent || 0)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm font-medium">{formatNumber(campaign.impressions || 0)} impressions</p>
                        <p className="text-xs text-muted-foreground">{campaign.ctr || 0}% CTR</p>
                      </div>
                      {getStatusBadge(campaign.status)}
                      <Link href={`/dashboard/advertiser/campaigns/${campaign.id}`}>
                        <Button variant="outline" size="sm">
                          <BarChart3 className="mr-2 h-4 w-4" />
                          View
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <Megaphone className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No campaigns yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Create your first campaign to start advertising
                  </p>
                  <Link href="/dashboard/advertiser/campaigns/create">
                    <Button>
                      <PlusCircle className="mr-2 h-4 w-4" />
                      Create Your First Campaign
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Performance Insights */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Conversion Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Total Conversions</span>
                  <span className="text-sm">{stats.conversions}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Cost per Conversion</span>
                  <span className="text-sm">{formatCurrency(stats.costPerConversion)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Conversion Rate</span>
                  <span className="text-sm">{((stats.conversions / stats.totalClicks) * 100).toFixed(2)}%</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Link href="/dashboard/advertiser/campaigns/create">
                  <Button className="w-full justify-start" variant="outline">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Create New Campaign
                  </Button>
                </Link>
                <Link href="/dashboard/advertiser/ads">
                  <Button className="w-full justify-start" variant="outline">
                    <Target className="mr-2 h-4 w-4" />
                    Manage Ads
                  </Button>
                </Link>
                <Link href="/dashboard/advertiser/analytics">
                  <Button className="w-full justify-start" variant="outline">
                    <BarChart3 className="mr-2 h-4 w-4" />
                    View Analytics
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdvertiserLayout>
  )
}
