"use client"

import { ThemeProvider } from "next-themes"
import { ReduxProvider } from "./redux-provider"
import { AuthSessionProvider } from "./session-provider"
import { Toaster } from "@/components/ui/sonner"

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ReduxProvider>
      <AuthSessionProvider>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          {children}
          <Toaster />
        </ThemeProvider>
      </AuthSessionProvider>
    </ReduxProvider>
  )
}
