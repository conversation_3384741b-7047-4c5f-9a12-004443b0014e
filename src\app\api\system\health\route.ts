import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { systemHealthChecker } from "@/lib/system-health-check"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // Allow unauthenticated access for basic health check
    const { searchParams } = new URL(request.url)
    const detailed = searchParams.get("detailed") === "true"

    if (detailed && (!session?.user?.id || session.user.role !== "ADMIN")) {
      return NextResponse.json(
        { message: "Detailed health check requires admin access" },
        { status: 403 }
      )
    }

    if (detailed) {
      // Full system health check (admin only)
      const healthReport = await systemHealthChecker.runFullHealthCheck()
      return NextResponse.json(healthReport)
    } else {
      // Basic health check (public)
      return NextResponse.json({
        status: "healthy",
        timestamp: new Date().toISOString(),
        message: "Ad Network Platform is operational"
      })
    }
  } catch (error) {
    console.error("Health check error:", error)
    return NextResponse.json(
      {
        status: "error",
        timestamp: new Date().toISOString(),
        message: "Health check failed",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    )
  }
}
