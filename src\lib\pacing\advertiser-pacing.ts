import { prisma } from "@/lib/prisma"
import { PacingStrategy, BidPacingStrategy } from "@prisma/client"

interface CampaignMetrics {
  impressions: number
  clicks: number
  conversions: number
  cost: number
  ctr: number
  conversionRate: number
  cpa: number
  roas: number
}

interface PacingRecommendation {
  newBidAmount: number
  budgetAdjustment: number
  reason: string
  confidence: number
}

export class AdvertiserPacingService {
  private static instance: AdvertiserPacingService

  static getInstance(): AdvertiserPacingService {
    if (!AdvertiserPacingService.instance) {
      AdvertiserPacingService.instance = new AdvertiserPacingService()
    }
    return AdvertiserPacingService.instance
  }

  /**
   * Initialize pacing for a new advertiser
   */
  async initializePacing(advertiserId: string): Promise<void> {
    const existingPacing = await prisma.advertiserPacing.findUnique({
      where: { advertiserId }
    })

    if (!existingPacing) {
      await prisma.advertiserPacing.create({
        data: {
          advertiserId,
          dailyBudgetPacing: PacingStrategy.EVEN,
          weeklyBudgetPacing: PacingStrategy.EVEN,
          monthlyBudgetPacing: PacingStrategy.EVEN,
          bidPacingStrategy: BidPacingStrategy.PERFORMANCE_BASED,
          targetCTR: 2.0,
          targetConversionRate: 5.0,
          maxBidIncrease: 0.5,
          maxBidDecrease: 0.3
        }
      })
    }
  }

  /**
   * Run pacing optimization for all active advertisers
   */
  async runPacingOptimization(): Promise<void> {
    console.log("Running advertiser pacing optimization...")

    const activeAdvertisers = await prisma.advertiserProfile.findMany({
      where: {
        campaigns: {
          some: { status: "ACTIVE" }
        }
      },
      include: {
        campaigns: {
          where: { status: "ACTIVE" },
          include: {
            ads: true,
            adPlacements: true
          }
        },
        advertiserPacing: true
      }
    })

    for (const advertiser of activeAdvertisers) {
      await this.optimizeAdvertiserPacing(advertiser)
    }

    console.log(`Optimized pacing for ${activeAdvertisers.length} advertisers`)
  }

  /**
   * Optimize pacing for a specific advertiser
   */
  private async optimizeAdvertiserPacing(advertiser: any): Promise<void> {
    if (!advertiser.advertiserPacing) {
      await this.initializePacing(advertiser.id)
      return
    }

    const pacing = advertiser.advertiserPacing

    for (const campaign of advertiser.campaigns) {
      // 1. Calculate current performance metrics
      const metrics = await this.calculateCampaignMetrics(campaign)

      // 2. Generate pacing recommendations
      const recommendation = this.generatePacingRecommendation(
        campaign,
        metrics,
        pacing
      )

      // 3. Apply recommendations if confidence is high enough
      if (recommendation.confidence > 0.7) {
        await this.applyPacingRecommendation(campaign.id, recommendation)
      }

      // 4. Adjust daily budget pacing
      await this.adjustDailyBudgetPacing(campaign, metrics, pacing)

      // 5. Monitor budget exhaustion risk
      await this.monitorBudgetExhaustion(campaign, metrics)
    }

    // Update last optimized timestamp
    await prisma.advertiserPacing.update({
      where: { advertiserId: advertiser.id },
      data: { lastOptimized: new Date() }
    })
  }

  /**
   * Calculate comprehensive campaign metrics
   */
  private async calculateCampaignMetrics(campaign: any): Promise<CampaignMetrics> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)

    // Get click data from enhanced tracking
    const clickData = await prisma.clickTracking.aggregate({
      where: {
        campaignId: campaign.id,
        createdAt: { gte: thirtyDaysAgo },
        isFraudulent: false
      },
      _count: { id: true },
      _sum: { bidAmount: true }
    })

    // Get impression data
    const impressionData = await prisma.adPlacement.aggregate({
      where: {
        campaignId: campaign.id,
        createdAt: { gte: thirtyDaysAgo }
      },
      _sum: {
        impressions: true,
        clicks: true,
        conversions: true,
        cost: true
      }
    })

    const impressions = impressionData._sum.impressions || 0
    const clicks = clickData._count.id || 0
    const conversions = impressionData._sum.conversions || 0
    const cost = clickData._sum.bidAmount || 0

    const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0
    const conversionRate = clicks > 0 ? (conversions / clicks) * 100 : 0
    const cpa = conversions > 0 ? cost / conversions : 0
    const roas = cost > 0 ? (conversions * 100) / cost : 0 // Assuming ₹100 per conversion

    return {
      impressions,
      clicks,
      conversions,
      cost,
      ctr,
      conversionRate,
      cpa,
      roas
    }
  }

  /**
   * Generate intelligent pacing recommendations
   */
  private generatePacingRecommendation(
    campaign: any,
    metrics: CampaignMetrics,
    pacing: any
  ): PacingRecommendation {
    let newBidAmount = campaign.bidAmount
    let budgetAdjustment = 0
    let reason = "No changes needed"
    let confidence = 0.5

    const { targetCTR, targetConversionRate, maxBidIncrease, maxBidDecrease } = pacing

    // Performance-based bid adjustments
    if (pacing.bidPacingStrategy === BidPacingStrategy.PERFORMANCE_BASED) {
      // High performance - increase bids
      if (metrics.ctr > targetCTR * 1.5 && metrics.conversionRate > targetConversionRate * 1.2) {
        const increase = Math.min(maxBidIncrease, 0.3)
        newBidAmount = campaign.bidAmount * (1 + increase)
        reason = `High performance (CTR: ${metrics.ctr.toFixed(2)}%, Conv: ${metrics.conversionRate.toFixed(2)}%) - increasing bid`
        confidence = 0.9
      }
      // Low performance - decrease bids
      else if (metrics.ctr < targetCTR * 0.5 || metrics.conversionRate < targetConversionRate * 0.5) {
        const decrease = Math.min(maxBidDecrease, 0.2)
        newBidAmount = campaign.bidAmount * (1 - decrease)
        reason = `Low performance (CTR: ${metrics.ctr.toFixed(2)}%, Conv: ${metrics.conversionRate.toFixed(2)}%) - decreasing bid`
        confidence = 0.8
      }
      // Moderate performance - small adjustments
      else if (metrics.ctr > targetCTR && metrics.conversionRate > targetConversionRate) {
        const increase = Math.min(maxBidIncrease * 0.5, 0.1)
        newBidAmount = campaign.bidAmount * (1 + increase)
        reason = `Good performance - small bid increase`
        confidence = 0.7
      }
    }

    // Budget-based adjustments
    if (pacing.bidPacingStrategy === BidPacingStrategy.BUDGET_BASED) {
      const budgetUtilization = metrics.cost / campaign.budget
      const timeProgress = this.calculateTimeProgress(campaign)

      // Underspending - increase bids
      if (budgetUtilization < timeProgress * 0.8) {
        const increase = Math.min(maxBidIncrease * 0.5, 0.15)
        newBidAmount = campaign.bidAmount * (1 + increase)
        reason = `Underspending (${(budgetUtilization * 100).toFixed(1)}% vs ${(timeProgress * 100).toFixed(1)}% time) - increasing bid`
        confidence = 0.8
      }
      // Overspending - decrease bids
      else if (budgetUtilization > timeProgress * 1.2) {
        const decrease = Math.min(maxBidDecrease * 0.5, 0.15)
        newBidAmount = campaign.bidAmount * (1 - decrease)
        reason = `Overspending (${(budgetUtilization * 100).toFixed(1)}% vs ${(timeProgress * 100).toFixed(1)}% time) - decreasing bid`
        confidence = 0.8
      }
    }

    // Ensure minimum bid
    newBidAmount = Math.max(newBidAmount, 0.10)

    return {
      newBidAmount: Math.round(newBidAmount * 100) / 100,
      budgetAdjustment,
      reason,
      confidence
    }
  }

  /**
   * Calculate campaign time progress (0-1)
   */
  private calculateTimeProgress(campaign: any): number {
    if (!campaign.startDate || !campaign.endDate) return 0.5

    const now = new Date()
    const start = new Date(campaign.startDate)
    const end = new Date(campaign.endDate)

    if (now < start) return 0
    if (now > end) return 1

    const totalDuration = end.getTime() - start.getTime()
    const elapsed = now.getTime() - start.getTime()

    return elapsed / totalDuration
  }

  /**
   * Apply pacing recommendations
   */
  private async applyPacingRecommendation(
    campaignId: string,
    recommendation: PacingRecommendation
  ): Promise<void> {
    const currentCampaign = await prisma.campaign.findUnique({
      where: { id: campaignId }
    })

    if (!currentCampaign) return

    await prisma.campaign.update({
      where: { id: campaignId },
      data: {
        bidAmount: recommendation.newBidAmount
      }
    })

    console.log(`Campaign ${campaignId}: ${currentCampaign.bidAmount} -> ${recommendation.newBidAmount} (${recommendation.reason})`)
  }

  /**
   * Adjust daily budget pacing
   */
  private async adjustDailyBudgetPacing(
    campaign: any,
    metrics: CampaignMetrics,
    pacing: any
  ): Promise<void> {
    if (!campaign.dailyBudget) return

    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // Get today's spending
    const todaySpending = await prisma.clickTracking.aggregate({
      where: {
        campaignId: campaign.id,
        createdAt: { gte: today },
        isFraudulent: false
      },
      _sum: { bidAmount: true }
    })

    const dailySpent = todaySpending._sum.bidAmount || 0
    const dailyBudgetUtilization = dailySpent / campaign.dailyBudget

    // Calculate time progress through the day
    const now = new Date()
    const dayProgress = (now.getHours() * 60 + now.getMinutes()) / (24 * 60)

    // Adjust pacing based on strategy
    if (pacing.dailyBudgetPacing === PacingStrategy.EVEN) {
      // Even pacing - should spend proportionally to time
      if (dailyBudgetUtilization < dayProgress * 0.7) {
        // Underspending - could increase bid slightly
        console.log(`Campaign ${campaign.id} underspending daily budget`)
      } else if (dailyBudgetUtilization > dayProgress * 1.3) {
        // Overspending - should decrease bid
        console.log(`Campaign ${campaign.id} overspending daily budget`)
      }
    }
  }

  /**
   * Monitor budget exhaustion risk
   */
  private async monitorBudgetExhaustion(campaign: any, metrics: CampaignMetrics): Promise<void> {
    const budgetUtilization = metrics.cost / campaign.budget
    const timeProgress = this.calculateTimeProgress(campaign)

    // Risk of budget exhaustion
    if (budgetUtilization > 0.9) {
      console.log(`⚠️ Campaign ${campaign.id} at 90% budget utilization`)
      
      // Auto-pause if budget exhausted
      if (budgetUtilization >= 1.0) {
        await prisma.campaign.update({
          where: { id: campaign.id },
          data: { status: "COMPLETED" }
        })
        console.log(`🛑 Campaign ${campaign.id} auto-paused due to budget exhaustion`)
      }
    }

    // Risk of underspending
    if (timeProgress > 0.8 && budgetUtilization < 0.5) {
      console.log(`📈 Campaign ${campaign.id} at risk of underspending (${(budgetUtilization * 100).toFixed(1)}% spent, ${(timeProgress * 100).toFixed(1)}% time elapsed)`)
    }
  }

  /**
   * Get pacing insights for advertiser dashboard
   */
  async getPacingInsights(advertiserId: string): Promise<any> {
    const pacing = await prisma.advertiserPacing.findUnique({
      where: { advertiserId }
    })

    if (!pacing) return null

    const campaigns = await prisma.campaign.findMany({
      where: { 
        advertiserId,
        status: "ACTIVE"
      },
      include: {
        adPlacements: true
      }
    })

    const insights = []

    for (const campaign of campaigns) {
      const metrics = await this.calculateCampaignMetrics(campaign)
      const timeProgress = this.calculateTimeProgress(campaign)
      const budgetUtilization = metrics.cost / campaign.budget

      insights.push({
        campaignId: campaign.id,
        campaignName: campaign.name,
        metrics,
        timeProgress,
        budgetUtilization,
        pacingStatus: this.getPacingStatus(budgetUtilization, timeProgress),
        recommendations: this.generatePacingRecommendation(campaign, metrics, pacing)
      })
    }

    return {
      pacingSettings: pacing,
      campaignInsights: insights,
      overallPerformance: this.calculateOverallPerformance(insights)
    }
  }

  /**
   * Get pacing status
   */
  private getPacingStatus(budgetUtilization: number, timeProgress: number): string {
    const ratio = budgetUtilization / timeProgress

    if (ratio > 1.3) return "OVERSPENDING"
    if (ratio < 0.7) return "UNDERSPENDING"
    return "ON_TRACK"
  }

  /**
   * Calculate overall performance across all campaigns
   */
  private calculateOverallPerformance(insights: any[]): any {
    if (insights.length === 0) return null

    const totals = insights.reduce((acc, insight) => ({
      impressions: acc.impressions + insight.metrics.impressions,
      clicks: acc.clicks + insight.metrics.clicks,
      conversions: acc.conversions + insight.metrics.conversions,
      cost: acc.cost + insight.metrics.cost
    }), { impressions: 0, clicks: 0, conversions: 0, cost: 0 })

    return {
      ...totals,
      averageCTR: totals.impressions > 0 ? (totals.clicks / totals.impressions) * 100 : 0,
      averageConversionRate: totals.clicks > 0 ? (totals.conversions / totals.clicks) * 100 : 0,
      averageCPA: totals.conversions > 0 ? totals.cost / totals.conversions : 0
    }
  }
}
