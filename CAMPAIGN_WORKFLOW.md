# Campaign Workflow & Status Management

## Campaign Status Flow

### Status Types
1. **DRAFT** - Initial state when campaign is created
2. **ACTIVE** - Campaign is running and serving ads
3. **PAUSED** - Campaign is temporarily stopped
4. **COMPLETED** - Campaign has finished (budget exhausted or end date reached)
5. **CANCELLED** - Campaign was manually cancelled

### Status Transition Rules

```
DRAFT → ACTIVE (when conditions are met)
ACTIVE → PAUSED (manual pause)
PAUSED → ACTIVE (manual resume)
ACTIVE → COMPLETED (automatic when budget/date limits reached)
ANY → CANCELLED (manual cancellation)
```

### Conditions for Campaign Activation

A campaign can only be activated (DRAFT → ACTIVE) when ALL of the following conditions are met:

1. **Has Active Ads**: Campaign must have at least one active ad
2. **Sufficient Balance**: Advertiser must have balance > ₹0
3. **Start Date**: Current date must be >= start date (if set)
4. **End Date**: Current date must be < end date (if set)
5. **Valid Targeting**: Must have target regions and categories selected

### Why Your Campaign is in DRAFT Status

When you create a campaign, it starts in **DRAFT** status because:

1. **No Ads Created Yet**: You need to create and activate ads for the campaign
2. **Safety Measure**: Prevents accidental spending before you're ready
3. **Review Opportunity**: Allows you to review settings before going live

## Ad Creation Workflow

### Step-by-Step Process

1. **Create Campaign** (Status: DRAFT)
   - Set budget, targeting, pricing model
   - Campaign is created but not active

2. **Create Ads for Campaign**
   - Go to Ads section
   - Create ads and assign them to your campaign
   - Set ad creative (image/video), title, description
   - Activate the ads

3. **Activate Campaign**
   - Once you have active ads, you can activate the campaign
   - Campaign status changes to ACTIVE
   - Ads start serving to users

### Current Ad Creation Options

**From Campaign Details Page:**
- Click "Add Ad" or "Create First Ad"
- Redirects to ads page with campaign pre-selected

**From Ads Page:**
- Create new ad
- Select campaign from dropdown
- Upload creative and set details

## Campaign Management Features

### Available Actions

#### For DRAFT Campaigns:
- ✅ Edit campaign settings
- ✅ Create/manage ads
- ✅ Activate (if conditions met)
- ✅ Delete campaign

#### For ACTIVE Campaigns:
- ✅ Pause campaign
- ✅ Edit limited settings (budget, bid amount)
- ✅ Manage ads (add/edit/pause)
- ✅ View real-time analytics
- ❌ Delete (must pause first)

#### For PAUSED Campaigns:
- ✅ Resume (activate)
- ✅ Edit settings
- ✅ Manage ads
- ✅ Cancel campaign

### Campaign Edit Functionality

**What You Can Edit:**
- Campaign name and description
- Budget (can increase, limited decrease for active campaigns)
- Daily budget
- Bid amount
- Target regions and categories
- Pricing model (for draft campaigns only)

**What You Cannot Edit:**
- Campaign ID
- Creation date
- Historical performance data
- Pricing model (for active campaigns)

## Ad Management Within Campaigns

### Ad Requirements for Campaign Activation

1. **Minimum Ads**: At least 1 active ad required
2. **Ad Formats**: Must match available ad spaces
3. **Creative Requirements**:
   - Image ads: Valid image URL
   - Video ads: Valid video URL
   - Proper dimensions (width x height)

### Ad Status Impact on Campaign

- **All Ads Inactive**: Campaign cannot be activated
- **Some Ads Active**: Campaign can run with active ads only
- **All Ads Paused**: Campaign effectively stops serving

## Automatic Campaign Management

### Auto-Pause Conditions

Campaigns automatically pause when:
1. **Budget Exhausted**: Total spent >= total budget
2. **Daily Budget Reached**: Daily spent >= daily budget (resumes next day)
3. **End Date Reached**: Current date >= end date (can be reactivated if end date is extended)
4. **Insufficient Balance**: Advertiser balance <= ₹0

### Auto-Complete Conditions

Campaigns automatically complete when:
1. **Budget Fully Spent**: Total budget consumed (terminal state)
2. **Manual Completion**: Advertiser marks as complete (terminal state)

**Note**: Campaigns that reach their end date are PAUSED (not COMPLETED) so they can be reactivated if the advertiser extends the end date.

## Best Practices

### Campaign Setup
1. **Start Small**: Begin with modest budgets to test performance
2. **Clear Targeting**: Be specific with regions and categories
3. **Multiple Ads**: Create 2-3 ads per campaign for A/B testing
4. **Monitor Closely**: Check performance daily for new campaigns

### Budget Management
1. **Set Daily Limits**: Use daily budgets to control spending
2. **Monitor Balance**: Keep sufficient balance for campaign duration
3. **Gradual Scaling**: Increase budgets gradually based on performance

### Ad Creative
1. **High Quality**: Use clear, engaging images/videos
2. **Relevant Content**: Match ad content to target audience
3. **Clear CTA**: Include compelling call-to-action
4. **Test Variations**: Try different creatives to optimize performance

## Troubleshooting Common Issues

### "Cannot Activate Campaign"

**Possible Causes:**
1. No active ads → Create and activate ads
2. Insufficient balance → Add funds to wallet
3. Start date in future → Wait or update start date
4. End date passed → Update end date
5. No targeting selected → Add regions/categories

### "Campaign Not Spending"

**Possible Causes:**
1. Bid too low → Increase bid amount
2. Targeting too narrow → Expand target audience
3. Poor ad quality → Improve ad creatives
4. Limited inventory → Try different ad formats/sizes

### "Ads Not Showing"

**Possible Causes:**
1. Campaign paused → Activate campaign
2. Ads inactive → Activate individual ads
3. Budget exhausted → Add more budget
4. Targeting mismatch → Review targeting settings

## API Endpoints for Campaign Management

### Campaign Operations
- `GET /api/advertiser/campaigns` - List campaigns
- `POST /api/advertiser/campaigns` - Create campaign
- `GET /api/advertiser/campaigns/[id]` - Get campaign details
- `PUT /api/advertiser/campaigns/[id]` - Update campaign
- `PUT /api/advertiser/campaigns/[id]/status` - Update campaign status
- `DELETE /api/advertiser/campaigns/[id]` - Delete campaign

### Ad Operations
- `GET /api/advertiser/ads?campaignId=[id]` - List campaign ads
- `POST /api/advertiser/ads` - Create ad
- `PUT /api/advertiser/ads/[id]` - Update ad
- `DELETE /api/advertiser/ads/[id]` - Delete ad

## Next Steps for Your Campaign

1. **Create Your First Ad**:
   - Go to the Ads section
   - Click "Create Ad"
   - Select your campaign
   - Upload image/video and set details

2. **Activate Your Campaign**:
   - Return to campaign details
   - Click "Activate" button
   - Campaign will start serving ads

3. **Monitor Performance**:
   - Check analytics regularly
   - Optimize based on performance data
   - Adjust budgets and targeting as needed

Your campaign workflow is now complete with full CRUD operations, status management, and proper validation rules!
