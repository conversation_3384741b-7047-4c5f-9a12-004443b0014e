// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

enum UserRole {
  PUBLISHER
  ADVERTISER
  ADMIN
}

enum CampaignStatus {
  DRAFT
  PENDING
  ACTIVE
  PAUSED
  COMPLETED
  CANCELLED
}

enum PacingStrategy {
  EVEN          // Spread budget/revenue evenly throughout the period
  FRONT_LOADED  // Spend/earn more at the beginning
  BACK_LOADED   // Spend/earn more at the end
  PERFORMANCE_BASED // Adjust based on performance metrics
  AGGRESSIVE    // Maximize spend/revenue quickly
  CONSERVATIVE  // Slow and steady approach
}

enum BidPacingStrategy {
  PERFORMANCE_BASED // Adjust bids based on CTR, conversion rate
  BUDGET_BASED     // Adjust bids based on remaining budget
  COMPETITION_BASED // Adjust bids based on competition
  TIME_BASED       // Adjust bids based on time of day/week
  FIXED            // Keep bids constant
}

enum AdServingStrategy {
  REVENUE_OPTIMIZED  // Prioritize highest revenue ads
  FILL_RATE_OPTIMIZED // Prioritize filling all ad spaces
  QUALITY_OPTIMIZED   // Prioritize high-quality ads
  BALANCED           // Balance between revenue and quality
}

enum AdFormat {
  BANNER
  VIDEO
  NATIVE
  POPUP
}

enum PricingModel {
  CPC
  CPM
  CPA
}

model Account {
  id                String  @id @default(auto()) @map("_id") @db.ObjectId
  userId            String  @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.String
  access_token      String? @db.String
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.String
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  sessionToken String   @unique
  userId       String   @db.ObjectId
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole?
  isOnboarded   Boolean   @default(false)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts         Account[]
  sessions         Session[]
  publisherProfile PublisherProfile?
  advertiserProfile AdvertiserProfile?
}

model PublisherProfile {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String   @unique @db.ObjectId
  websiteUrl  String
  websiteName String
  description String?
  category    String
  monthlyTraffic Int
  region      String
  apiKey      String   @unique
  apiSecret   String
  isActive    Boolean  @default(true)
  balance     Float    @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  adSpaces AdSpace[]
  clickTracking ClickTracking[]
  publisherPacing PublisherPacing?
}

model AdvertiserProfile {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String   @unique @db.ObjectId
  companyName String
  website     String?
  description String?
  industry    String
  budget      Float    @default(0)
  balance     Float    @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  campaigns Campaign[]
  paymentOrders PaymentOrder[]
  paymentTransactions PaymentTransaction[]
  walletTransactions WalletTransaction[]
  clickTracking ClickTracking[]
  advertiserPacing AdvertiserPacing?
}

model AdSpace {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  publisherId String   @db.ObjectId
  name        String
  format      AdFormat
  width       Int
  height      Int
  position    String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  publisher PublisherProfile @relation(fields: [publisherId], references: [id], onDelete: Cascade)
  adPlacements AdPlacement[]
  clickTracking ClickTracking[]
  frequencyTracking FrequencyTracking[]
}

model Campaign {
  id           String         @id @default(auto()) @map("_id") @db.ObjectId
  advertiserId String         @db.ObjectId
  name         String
  description  String?
  budget       Float
  dailyBudget  Float?
  bidAmount    Float
  pricingModel PricingModel
  targetRegions String[]
  targetCategories String[]
  status       CampaignStatus @default(DRAFT)
  startDate    DateTime?
  endDate      DateTime?

  // Frequency Management
  frequencyCap     Int      @default(5)  // Max impressions per user per day
  frequencyPeriod  String   @default("DAILY") // DAILY, WEEKLY, CAMPAIGN

  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt

  advertiser AdvertiserProfile @relation(fields: [advertiserId], references: [id], onDelete: Cascade)
  ads Ad[]
  adPlacements AdPlacement[]
  walletTransactions WalletTransaction[]
  clickTracking ClickTracking[]
  frequencyTracking FrequencyTracking[]
}

model Ad {
  id         String    @id @default(auto()) @map("_id") @db.ObjectId
  campaignId String    @db.ObjectId
  title      String
  description String?
  imageUrl   String?
  videoUrl   String?
  clickUrl   String
  format     AdFormat
  width      Int
  height     Int
  isActive   Boolean   @default(true)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  campaign Campaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  adPlacements AdPlacement[]
  clickTracking ClickTracking[]
  frequencyTracking FrequencyTracking[]
}

model AdPlacement {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  campaignId  String   @db.ObjectId
  adId        String   @db.ObjectId
  adSpaceId   String   @db.ObjectId
  isActive    Boolean  @default(true)
  impressions Int      @default(0)
  clicks      Int      @default(0)
  conversions Int      @default(0)
  revenue     Float    @default(0)
  cost        Float    @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  campaign Campaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  ad       Ad       @relation(fields: [adId], references: [id], onDelete: Cascade)
  adSpace  AdSpace  @relation(fields: [adSpaceId], references: [id], onDelete: Cascade)
}

model PaymentOrder {
  id               String   @id @default(auto()) @map("_id") @db.ObjectId
  advertiserId     String   @db.ObjectId
  amount           Float
  currency         String   @default("INR")
  razorpayOrderId  String   @unique
  status           String   @default("CREATED") // CREATED, PAID, FAILED, CANCELLED
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  advertiser AdvertiserProfile @relation(fields: [advertiserId], references: [id], onDelete: Cascade)
  transactions PaymentTransaction[]
  walletTransactions WalletTransaction[]
}

model PaymentTransaction {
  id                  String   @id @default(auto()) @map("_id") @db.ObjectId
  advertiserId        String   @db.ObjectId
  orderId             String   @db.ObjectId
  razorpayPaymentId   String?
  razorpaySignature   String?
  amount              Float
  currency            String   @default("INR")
  status              String   @default("PENDING") // PENDING, SUCCESS, FAILED
  failureReason       String?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  advertiser AdvertiserProfile @relation(fields: [advertiserId], references: [id], onDelete: Cascade)
  order      PaymentOrder      @relation(fields: [orderId], references: [id], onDelete: Cascade)
}

model WalletTransaction {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  advertiserId      String   @db.ObjectId
  type              String   // CREDIT, DEBIT
  amount            Float
  description       String
  balanceAfter      Float
  relatedOrderId    String?  @db.ObjectId
  relatedCampaignId String?  @db.ObjectId
  createdAt         DateTime @default(now())

  advertiser      AdvertiserProfile @relation(fields: [advertiserId], references: [id], onDelete: Cascade)
  relatedOrder    PaymentOrder?     @relation(fields: [relatedOrderId], references: [id], onDelete: SetNull)
  relatedCampaign Campaign?         @relation(fields: [relatedCampaignId], references: [id], onDelete: SetNull)
}

model VerificationToken {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Click Tracking Model for Fair Bidding
model ClickTracking {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  trackingId        String   @unique
  campaignId        String   @db.ObjectId
  adId              String   @db.ObjectId
  adSpaceId         String   @db.ObjectId
  publisherId       String   @db.ObjectId
  advertiserId      String   @db.ObjectId

  // User identification (for fraud prevention)
  ipAddress         String
  userAgent         String?
  fingerprint       String? // Browser fingerprint
  sessionId         String?

  // Click details
  clickedAt         DateTime @default(now())
  referrer          String?
  destinationUrl    String

  // Fraud detection
  isFraudulent      Boolean @default(false)
  fraudReason       String?
  isValidated       Boolean @default(false)

  // Billing information
  bidAmount         Float
  publisherRevenue  Float
  platformRevenue   Float

  // Geographic data
  country           String?
  region            String?
  city              String?

  // Device information
  deviceType        String? // mobile, desktop, tablet
  browser           String?
  os                String?

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  campaign    Campaign          @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  ad          Ad                @relation(fields: [adId], references: [id], onDelete: Cascade)
  adSpace     AdSpace           @relation(fields: [adSpaceId], references: [id], onDelete: Cascade)
  publisher   PublisherProfile  @relation(fields: [publisherId], references: [id], onDelete: Cascade)
  advertiser  AdvertiserProfile @relation(fields: [advertiserId], references: [id], onDelete: Cascade)

  @@map("click_tracking")
}

// Advertiser Pacing Configuration
model AdvertiserPacing {
  id                    String   @id @default(auto()) @map("_id") @db.ObjectId
  advertiserId          String   @unique @db.ObjectId

  // Budget pacing settings
  dailyBudgetPacing     PacingStrategy @default(EVEN)
  weeklyBudgetPacing    PacingStrategy @default(EVEN)
  monthlyBudgetPacing   PacingStrategy @default(EVEN)

  // Bid pacing settings
  bidPacingStrategy     BidPacingStrategy @default(PERFORMANCE_BASED)
  maxBidIncrease        Float @default(0.5) // 50% max increase
  maxBidDecrease        Float @default(0.3) // 30% max decrease

  // Performance thresholds
  targetCTR             Float @default(2.0) // 2% target CTR
  targetConversionRate  Float @default(5.0) // 5% target conversion
  targetCPA             Float? // Target cost per acquisition
  targetROAS            Float? // Target return on ad spend

  // Pacing controls
  isActive              Boolean @default(true)
  lastOptimized         DateTime @default(now())
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  advertiser AdvertiserProfile @relation(fields: [advertiserId], references: [id], onDelete: Cascade)

  @@map("advertiser_pacing")
}

// Publisher Pacing Configuration
model PublisherPacing {
  id                    String   @id @default(auto()) @map("_id") @db.ObjectId
  publisherId           String   @unique @db.ObjectId

  // Revenue pacing settings
  dailyRevenuePacing    PacingStrategy @default(EVEN)
  weeklyRevenuePacing   PacingStrategy @default(EVEN)
  monthlyRevenuePacing  PacingStrategy @default(EVEN)

  // Ad serving pacing
  adServingStrategy     AdServingStrategy @default(REVENUE_OPTIMIZED)
  minFillRate           Float @default(0.8) // 80% minimum fill rate
  maxAdFrequency        Int @default(3) // Max ads per user per hour

  // Revenue targets
  dailyRevenueTarget    Float?
  weeklyRevenueTarget   Float?
  monthlyRevenueTarget  Float?

  // Quality controls
  minAdQualityScore     Float @default(0.7) // Minimum ad quality score
  blockLowPerformingAds Boolean @default(true)

  // Pacing controls
  isActive              Boolean @default(true)
  lastOptimized         DateTime @default(now())
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  publisher PublisherProfile @relation(fields: [publisherId], references: [id], onDelete: Cascade)

  @@map("publisher_pacing")
}

// Frequency Tracking Model
model FrequencyTracking {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  userId        String?  // For logged-in users
  fingerprint   String   // Browser fingerprint for anonymous users
  campaignId    String   @db.ObjectId
  adId          String   @db.ObjectId
  adSpaceId     String   @db.ObjectId

  // Frequency data
  impressions   Int      @default(1)
  lastSeen      DateTime @default(now())
  period        String   // DAILY, WEEKLY, CAMPAIGN
  periodStart   DateTime
  periodEnd     DateTime

  // User context
  ipAddress     String
  userAgent     String?
  deviceType    String?

  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  campaign      Campaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  ad            Ad       @relation(fields: [adId], references: [id], onDelete: Cascade)
  adSpace       AdSpace  @relation(fields: [adSpaceId], references: [id], onDelete: Cascade)

  // Compound index for efficient frequency lookups
  @@index([fingerprint, campaignId, periodStart, periodEnd])
  @@index([userId, campaignId, periodStart, periodEnd])
  @@map("frequency_tracking")
}
