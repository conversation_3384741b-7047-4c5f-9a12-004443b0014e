import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { z } from "zod"
import { AdFormat } from "@prisma/client"

const adSpaceUpdateSchema = z.object({
  name: z.string().min(1, "Ad space name is required").optional(),
  format: z.enum(["BANNER", "VIDEO", "NATIVE", "POPUP"]).optional(),
  width: z.number().min(1, "Width must be greater than 0").optional(),
  height: z.number().min(1, "Height must be greater than 0").optional(),
  position: z.string().min(1, "Position is required").optional(),
  isActive: z.boolean().optional(),
})

// GET /api/publisher/ad-spaces/[id] - Get specific ad space
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "PUBLISHER") {
      return NextResponse.json(
        { message: "Only publishers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get ad space and verify ownership
    const adSpace = await prisma.adSpace.findUnique({
      where: { id: params.id },
      include: {
        publisher: {
          include: {
            user: true,
          },
        },
        adPlacements: {
          include: {
            campaign: {
              select: {
                name: true,
                pricingModel: true,
              },
            },
            ad: {
              select: {
                title: true,
                format: true,
              },
            },
          },
        },
      },
    })

    if (!adSpace) {
      return NextResponse.json(
        { message: "Ad space not found" },
        { status: 404 }
      )
    }

    // Check if user owns this ad space
    if (adSpace.publisher.user.id !== session.user.id) {
      return NextResponse.json(
        { message: "Forbidden" },
        { status: 403 }
      )
    }

    // Calculate metrics
    const totalImpressions = adSpace.adPlacements.reduce((sum, p) => sum + p.impressions, 0)
    const totalClicks = adSpace.adPlacements.reduce((sum, p) => sum + p.clicks, 0)
    const totalConversions = adSpace.adPlacements.reduce((sum, p) => sum + p.conversions, 0)
    const totalRevenue = adSpace.adPlacements.reduce((sum, p) => sum + p.revenue, 0)
    const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0

    return NextResponse.json({
      adSpace: {
        id: adSpace.id,
        name: adSpace.name,
        format: adSpace.format,
        width: adSpace.width,
        height: adSpace.height,
        position: adSpace.position,
        isActive: adSpace.isActive,
        createdAt: adSpace.createdAt,
        updatedAt: adSpace.updatedAt,
        // Metrics
        impressions: totalImpressions,
        clicks: totalClicks,
        conversions: totalConversions,
        earnings: parseFloat(totalRevenue.toFixed(2)),
        ctr: parseFloat(ctr.toFixed(2)),
        // Active placements
        activePlacements: adSpace.adPlacements.filter(p => p.isActive).length,
      },
    })
  } catch (error) {
    console.error("Ad space fetch error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/publisher/ad-spaces/[id] - Update ad space
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "PUBLISHER") {
      return NextResponse.json(
        { message: "Only publishers can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = adSpaceUpdateSchema.parse(body)

    // Get ad space and verify ownership
    const adSpace = await prisma.adSpace.findUnique({
      where: { id: params.id },
      include: {
        publisher: {
          include: {
            user: true,
          },
        },
      },
    })

    if (!adSpace) {
      return NextResponse.json(
        { message: "Ad space not found" },
        { status: 404 }
      )
    }

    // Check if user owns this ad space
    if (adSpace.publisher.user.id !== session.user.id) {
      return NextResponse.json(
        { message: "Forbidden" },
        { status: 403 }
      )
    }

    // Update ad space
    const updatedAdSpace = await prisma.adSpace.update({
      where: { id: params.id },
      data: {
        ...(validatedData.name && { name: validatedData.name }),
        ...(validatedData.format && { format: validatedData.format as AdFormat }),
        ...(validatedData.width && { width: validatedData.width }),
        ...(validatedData.height && { height: validatedData.height }),
        ...(validatedData.position && { position: validatedData.position }),
        ...(validatedData.isActive !== undefined && { isActive: validatedData.isActive }),
      },
    })

    return NextResponse.json({
      message: "Ad space updated successfully",
      adSpace: {
        id: updatedAdSpace.id,
        name: updatedAdSpace.name,
        format: updatedAdSpace.format,
        width: updatedAdSpace.width,
        height: updatedAdSpace.height,
        position: updatedAdSpace.position,
        isActive: updatedAdSpace.isActive,
        updatedAt: updatedAdSpace.updatedAt,
      },
    })
  } catch (error) {
    console.error("Ad space update error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid request data", errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// DELETE /api/publisher/ad-spaces/[id] - Delete ad space
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "PUBLISHER") {
      return NextResponse.json(
        { message: "Only publishers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get ad space and verify ownership
    const adSpace = await prisma.adSpace.findUnique({
      where: { id: params.id },
      include: {
        publisher: {
          include: {
            user: true,
          },
        },
        adPlacements: true,
      },
    })

    if (!adSpace) {
      return NextResponse.json(
        { message: "Ad space not found" },
        { status: 404 }
      )
    }

    // Check if user owns this ad space
    if (adSpace.publisher.user.id !== session.user.id) {
      return NextResponse.json(
        { message: "Forbidden" },
        { status: 403 }
      )
    }

    // Check if ad space has active placements
    const activePlacements = adSpace.adPlacements.filter(p => p.isActive)
    if (activePlacements.length > 0) {
      return NextResponse.json(
        { 
          message: "Cannot delete ad space with active placements",
          activePlacements: activePlacements.length 
        },
        { status: 400 }
      )
    }

    // Delete ad space (this will cascade delete placements due to schema)
    await prisma.adSpace.delete({
      where: { id: params.id },
    })

    return NextResponse.json({
      message: "Ad space deleted successfully",
    })
  } catch (error) {
    console.error("Ad space deletion error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
