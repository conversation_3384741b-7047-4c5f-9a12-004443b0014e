import { prisma } from "@/lib/prisma"
import { PacingStrategy, AdServingStrategy } from "@prisma/client"

interface PublisherMetrics {
  impressions: number
  clicks: number
  revenue: number
  fillRate: number
  ctr: number
  rpm: number // Revenue per mille (1000 impressions)
  qualityScore: number
}

interface AdSpacePerformance {
  adSpaceId: string
  name: string
  metrics: PublisherMetrics
  recommendations: string[]
}

export class PublisherPacingService {
  private static instance: PublisherPacingService

  static getInstance(): PublisherPacingService {
    if (!PublisherPacingService.instance) {
      PublisherPacingService.instance = new PublisherPacingService()
    }
    return PublisherPacingService.instance
  }

  /**
   * Initialize pacing for a new publisher
   */
  async initializePacing(publisherId: string): Promise<void> {
    const existingPacing = await prisma.publisherPacing.findUnique({
      where: { publisherId }
    })

    if (!existingPacing) {
      await prisma.publisherPacing.create({
        data: {
          publisherId,
          dailyRevenuePacing: PacingStrategy.EVEN,
          weeklyRevenuePacing: PacingStrategy.EVEN,
          monthlyRevenuePacing: PacingStrategy.EVEN,
          adServingStrategy: AdServingStrategy.REVENUE_OPTIMIZED,
          minFillRate: 0.8,
          maxAdFrequency: 3,
          minAdQualityScore: 0.7,
          blockLowPerformingAds: true
        }
      })
    }
  }

  /**
   * Run pacing optimization for all active publishers
   */
  async runPacingOptimization(): Promise<void> {
    console.log("Running publisher pacing optimization...")

    const activePublishers = await prisma.publisherProfile.findMany({
      where: {
        isActive: true,
        adSpaces: {
          some: { isActive: true }
        }
      },
      include: {
        adSpaces: {
          where: { isActive: true },
          include: {
            adPlacements: true
          }
        },
        publisherPacing: true
      }
    })

    for (const publisher of activePublishers) {
      await this.optimizePublisherPacing(publisher)
    }

    console.log(`Optimized pacing for ${activePublishers.length} publishers`)
  }

  /**
   * Optimize pacing for a specific publisher
   */
  private async optimizePublisherPacing(publisher: any): Promise<void> {
    if (!publisher.publisherPacing) {
      await this.initializePacing(publisher.id)
      return
    }

    const pacing = publisher.publisherPacing

    // 1. Analyze overall publisher performance
    const overallMetrics = await this.calculatePublisherMetrics(publisher.id)

    // 2. Analyze individual ad space performance
    const adSpacePerformances: AdSpacePerformance[] = []
    for (const adSpace of publisher.adSpaces) {
      const metrics = await this.calculateAdSpaceMetrics(adSpace.id)
      const recommendations = this.generateAdSpaceRecommendations(adSpace, metrics, pacing)
      
      adSpacePerformances.push({
        adSpaceId: adSpace.id,
        name: adSpace.name,
        metrics,
        recommendations
      })
    }

    // 3. Optimize ad serving strategy
    await this.optimizeAdServingStrategy(publisher.id, overallMetrics, pacing)

    // 4. Adjust revenue pacing
    await this.adjustRevenuePacing(publisher.id, overallMetrics, pacing)

    // 5. Monitor fill rate and quality
    await this.monitorFillRateAndQuality(publisher.id, adSpacePerformances, pacing)

    // Update last optimized timestamp
    await prisma.publisherPacing.update({
      where: { publisherId: publisher.id },
      data: { lastOptimized: new Date() }
    })
  }

  /**
   * Calculate comprehensive publisher metrics
   */
  private async calculatePublisherMetrics(publisherId: string): Promise<PublisherMetrics> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)

    // Get click data from enhanced tracking
    const clickData = await prisma.clickTracking.aggregate({
      where: {
        publisherId,
        createdAt: { gte: thirtyDaysAgo },
        isFraudulent: false
      },
      _count: { id: true },
      _sum: { publisherRevenue: true }
    })

    // Get impression and placement data
    const placementData = await prisma.adPlacement.aggregate({
      where: {
        adSpace: { publisherId },
        createdAt: { gte: thirtyDaysAgo }
      },
      _sum: {
        impressions: true,
        clicks: true,
        revenue: true
      }
    })

    // Get total ad requests (for fill rate calculation)
    const totalAdSpaces = await prisma.adSpace.count({
      where: { publisherId, isActive: true }
    })

    const impressions = placementData._sum.impressions || 0
    const clicks = clickData._count.id || 0
    const revenue = clickData._sum.publisherRevenue || 0

    const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0
    const rpm = impressions > 0 ? (revenue / impressions) * 1000 : 0
    const fillRate = totalAdSpaces > 0 ? Math.min(impressions / (totalAdSpaces * 1000), 1) : 0 // Assuming 1000 requests per ad space
    
    // Calculate quality score based on CTR and fraud rate
    const totalClicks = await prisma.clickTracking.count({
      where: { publisherId, createdAt: { gte: thirtyDaysAgo } }
    })
    const fraudRate = totalClicks > 0 ? 1 - (clicks / totalClicks) : 0
    const qualityScore = Math.max(0, Math.min(1, (ctr / 5) * 0.7 + (1 - fraudRate) * 0.3))

    return {
      impressions,
      clicks,
      revenue,
      fillRate,
      ctr,
      rpm,
      qualityScore
    }
  }

  /**
   * Calculate metrics for a specific ad space
   */
  private async calculateAdSpaceMetrics(adSpaceId: string): Promise<PublisherMetrics> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)

    const clickData = await prisma.clickTracking.aggregate({
      where: {
        adSpaceId,
        createdAt: { gte: thirtyDaysAgo },
        isFraudulent: false
      },
      _count: { id: true },
      _sum: { publisherRevenue: true }
    })

    const placementData = await prisma.adPlacement.aggregate({
      where: {
        adSpaceId,
        createdAt: { gte: thirtyDaysAgo }
      },
      _sum: {
        impressions: true,
        clicks: true,
        revenue: true
      }
    })

    const impressions = placementData._sum.impressions || 0
    const clicks = clickData._count.id || 0
    const revenue = clickData._sum.publisherRevenue || 0

    const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0
    const rpm = impressions > 0 ? (revenue / impressions) * 1000 : 0
    const fillRate = impressions > 100 ? 0.9 : impressions / 100 // Mock fill rate calculation

    // Quality score for this ad space
    const totalClicks = await prisma.clickTracking.count({
      where: { adSpaceId, createdAt: { gte: thirtyDaysAgo } }
    })
    const fraudRate = totalClicks > 0 ? 1 - (clicks / totalClicks) : 0
    const qualityScore = Math.max(0, Math.min(1, (ctr / 5) * 0.7 + (1 - fraudRate) * 0.3))

    return {
      impressions,
      clicks,
      revenue,
      fillRate,
      ctr,
      rpm,
      qualityScore
    }
  }

  /**
   * Generate recommendations for ad space optimization
   */
  private generateAdSpaceRecommendations(
    adSpace: any,
    metrics: PublisherMetrics,
    pacing: any
  ): string[] {
    const recommendations: string[] = []

    // Fill rate recommendations
    if (metrics.fillRate < pacing.minFillRate) {
      recommendations.push(`Low fill rate (${(metrics.fillRate * 100).toFixed(1)}%). Consider adjusting ad space dimensions or targeting.`)
    }

    // CTR recommendations
    if (metrics.ctr < 1.0) {
      recommendations.push(`Low CTR (${metrics.ctr.toFixed(2)}%). Consider improving ad placement or design.`)
    } else if (metrics.ctr > 5.0) {
      recommendations.push(`Excellent CTR (${metrics.ctr.toFixed(2)}%). Consider increasing ad space priority.`)
    }

    // Revenue recommendations
    if (metrics.rpm < 10) {
      recommendations.push(`Low RPM (₹${metrics.rpm.toFixed(2)}). Consider premium ad formats or better targeting.`)
    } else if (metrics.rpm > 50) {
      recommendations.push(`High RPM (₹${metrics.rpm.toFixed(2)}). Consider adding more similar ad spaces.`)
    }

    // Quality recommendations
    if (metrics.qualityScore < pacing.minAdQualityScore) {
      recommendations.push(`Quality score below threshold (${(metrics.qualityScore * 100).toFixed(1)}%). Review ad content and user experience.`)
    }

    // Position-specific recommendations
    if (adSpace.position === 'header' && metrics.ctr < 2.0) {
      recommendations.push("Header ads typically perform better. Consider A/B testing different creative formats.")
    }

    if (adSpace.position === 'sidebar' && metrics.fillRate < 0.7) {
      recommendations.push("Sidebar ads may need better integration with content flow.")
    }

    return recommendations
  }

  /**
   * Optimize ad serving strategy
   */
  private async optimizeAdServingStrategy(
    publisherId: string,
    metrics: PublisherMetrics,
    pacing: any
  ): Promise<void> {
    let newStrategy = pacing.adServingStrategy

    // Switch to revenue optimization if quality is good
    if (metrics.qualityScore > 0.8 && metrics.ctr > 2.0) {
      newStrategy = AdServingStrategy.REVENUE_OPTIMIZED
    }
    // Switch to quality optimization if metrics are poor
    else if (metrics.qualityScore < 0.6 || metrics.ctr < 1.0) {
      newStrategy = AdServingStrategy.QUALITY_OPTIMIZED
    }
    // Balanced approach for moderate performance
    else {
      newStrategy = AdServingStrategy.BALANCED
    }

    if (newStrategy !== pacing.adServingStrategy) {
      await prisma.publisherPacing.update({
        where: { publisherId },
        data: { adServingStrategy: newStrategy }
      })
      console.log(`Publisher ${publisherId} ad serving strategy changed to ${newStrategy}`)
    }
  }

  /**
   * Adjust revenue pacing based on performance
   */
  private async adjustRevenuePacing(
    publisherId: string,
    metrics: PublisherMetrics,
    pacing: any
  ): Promise<void> {
    // Calculate revenue targets if not set
    if (!pacing.dailyRevenueTarget) {
      const estimatedDailyRevenue = metrics.revenue / 30 // 30-day average
      
      await prisma.publisherPacing.update({
        where: { publisherId },
        data: {
          dailyRevenueTarget: estimatedDailyRevenue * 1.1, // 10% growth target
          weeklyRevenueTarget: estimatedDailyRevenue * 7 * 1.1,
          monthlyRevenueTarget: estimatedDailyRevenue * 30 * 1.1
        }
      })
    }

    // Adjust pacing strategy based on performance
    let newPacingStrategy = pacing.dailyRevenuePacing

    if (metrics.rpm > 30 && metrics.qualityScore > 0.8) {
      // High performance - aggressive pacing
      newPacingStrategy = PacingStrategy.AGGRESSIVE
    } else if (metrics.rpm < 10 || metrics.qualityScore < 0.6) {
      // Poor performance - conservative pacing
      newPacingStrategy = PacingStrategy.CONSERVATIVE
    } else {
      // Moderate performance - even pacing
      newPacingStrategy = PacingStrategy.EVEN
    }

    if (newPacingStrategy !== pacing.dailyRevenuePacing) {
      await prisma.publisherPacing.update({
        where: { publisherId },
        data: { dailyRevenuePacing: newPacingStrategy }
      })
      console.log(`Publisher ${publisherId} revenue pacing changed to ${newPacingStrategy}`)
    }
  }

  /**
   * Monitor fill rate and quality metrics
   */
  private async monitorFillRateAndQuality(
    publisherId: string,
    adSpacePerformances: AdSpacePerformance[],
    pacing: any
  ): Promise<void> {
    // Check overall fill rate
    const averageFillRate = adSpacePerformances.reduce((sum, perf) => sum + perf.metrics.fillRate, 0) / adSpacePerformances.length

    if (averageFillRate < pacing.minFillRate) {
      console.log(`⚠️ Publisher ${publisherId} fill rate below target: ${(averageFillRate * 100).toFixed(1)}%`)
    }

    // Check quality scores
    const lowQualitySpaces = adSpacePerformances.filter(perf => perf.metrics.qualityScore < pacing.minAdQualityScore)
    
    if (lowQualitySpaces.length > 0) {
      console.log(`⚠️ Publisher ${publisherId} has ${lowQualitySpaces.length} low-quality ad spaces`)
      
      // Optionally disable low-performing ad spaces
      if (pacing.blockLowPerformingAds) {
        for (const lowQualitySpace of lowQualitySpaces) {
          if (lowQualitySpace.metrics.qualityScore < 0.3) {
            await prisma.adSpace.update({
              where: { id: lowQualitySpace.adSpaceId },
              data: { isActive: false }
            })
            console.log(`🚫 Disabled low-quality ad space: ${lowQualitySpace.name}`)
          }
        }
      }
    }

    // Monitor revenue trends
    const highPerformingSpaces = adSpacePerformances.filter(perf => perf.metrics.rpm > 40)
    if (highPerformingSpaces.length > 0) {
      console.log(`🚀 Publisher ${publisherId} has ${highPerformingSpaces.length} high-performing ad spaces`)
    }
  }

  /**
   * Get pacing insights for publisher dashboard
   */
  async getPacingInsights(publisherId: string): Promise<any> {
    const pacing = await prisma.publisherPacing.findUnique({
      where: { publisherId }
    })

    if (!pacing) return null

    const overallMetrics = await this.calculatePublisherMetrics(publisherId)
    
    const adSpaces = await prisma.adSpace.findMany({
      where: { publisherId, isActive: true }
    })

    const adSpaceInsights = []
    for (const adSpace of adSpaces) {
      const metrics = await this.calculateAdSpaceMetrics(adSpace.id)
      const recommendations = this.generateAdSpaceRecommendations(adSpace, metrics, pacing)
      
      adSpaceInsights.push({
        adSpace,
        metrics,
        recommendations,
        status: this.getAdSpaceStatus(metrics, pacing)
      })
    }

    return {
      pacingSettings: pacing,
      overallMetrics,
      adSpaceInsights,
      revenueProjection: this.calculateRevenueProjection(overallMetrics, pacing),
      optimizationTips: this.generateOptimizationTips(overallMetrics, adSpaceInsights)
    }
  }

  /**
   * Get ad space status
   */
  private getAdSpaceStatus(metrics: PublisherMetrics, pacing: any): string {
    if (metrics.qualityScore < pacing.minAdQualityScore) return "NEEDS_ATTENTION"
    if (metrics.fillRate < pacing.minFillRate) return "LOW_FILL_RATE"
    if (metrics.rpm > 40) return "HIGH_PERFORMING"
    if (metrics.ctr > 3.0) return "EXCELLENT_CTR"
    return "NORMAL"
  }

  /**
   * Calculate revenue projection
   */
  private calculateRevenueProjection(metrics: PublisherMetrics, pacing: any): any {
    const dailyRevenue = metrics.revenue / 30
    const projectedMonthly = dailyRevenue * 30
    const targetMonthly = pacing.monthlyRevenueTarget || projectedMonthly * 1.1

    return {
      currentDaily: dailyRevenue,
      projectedMonthly,
      targetMonthly,
      growthNeeded: targetMonthly > projectedMonthly ? ((targetMonthly - projectedMonthly) / projectedMonthly) * 100 : 0
    }
  }

  /**
   * Generate optimization tips
   */
  private generateOptimizationTips(overallMetrics: PublisherMetrics, adSpaceInsights: any[]): string[] {
    const tips: string[] = []

    if (overallMetrics.fillRate < 0.8) {
      tips.push("Improve fill rate by optimizing ad space dimensions and targeting")
    }

    if (overallMetrics.ctr < 2.0) {
      tips.push("Enhance ad placement and integration with content to improve CTR")
    }

    if (overallMetrics.rpm < 20) {
      tips.push("Consider premium ad formats and better audience targeting to increase RPM")
    }

    const lowPerformingSpaces = adSpaceInsights.filter(insight => insight.metrics.qualityScore < 0.6)
    if (lowPerformingSpaces.length > 0) {
      tips.push(`Review ${lowPerformingSpaces.length} underperforming ad spaces for optimization opportunities`)
    }

    const highPerformingSpaces = adSpaceInsights.filter(insight => insight.metrics.rpm > 40)
    if (highPerformingSpaces.length > 0) {
      tips.push(`Replicate success patterns from ${highPerformingSpaces.length} high-performing ad spaces`)
    }

    return tips
  }
}
