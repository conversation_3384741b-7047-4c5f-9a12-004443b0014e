import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// GET /api/health - Health check endpoint
export async function GET(request: NextRequest) {
  try {
    // Test database connection
    await prisma.$connect()
    
    // Get basic stats
    const stats = await Promise.all([
      prisma.user.count(),
      prisma.publisherProfile.count(),
      prisma.advertiserProfile.count(),
      prisma.campaign.count({ where: { status: 'ACTIVE' } }),
      prisma.adSpace.count({ where: { isActive: true } })
    ])

    const [totalUsers, totalPublishers, totalAdvertisers, activeCampaigns, activeAdSpaces] = stats

    return NextResponse.json({
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
      database: {
        connected: true,
        stats: {
          totalUsers,
          totalPublishers,
          totalAdvertisers,
          activeCampaigns,
          activeAdSpaces
        }
      },
      services: {
        adServing: true,
        bidding: true,
        tracking: true
      }
    })

  } catch (error) {
    console.error("Health check failed:", error)
    
    return NextResponse.json(
      {
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : "Unknown error",
        database: {
          connected: false
        }
      },
      { status: 500 }
    )
  }
}
