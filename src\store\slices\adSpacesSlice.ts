import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { AdFormat } from '@/lib/types'

interface AdSpace {
  id: string
  name: string
  format: AdFormat
  width: number
  height: number
  position: string
  isActive: boolean
  impressions: number
  clicks: number
  conversions: number
  earnings: number
  ctr: number
  createdAt: Date
  updatedAt?: Date
}

interface AdSpaceSuggestion {
  name: string
  format: AdFormat
  width: number
  height: number
  position: string
  selector: string
  confidence: number
  reasoning: string
}

interface ScanResult {
  success: boolean
  suggestions: AdSpaceSuggestion[]
  websiteInfo: {
    title: string
    description: string
    hasHeader: boolean
    hasSidebar: boolean
    hasFooter: boolean
    contentWidth: number
    mobileResponsive: boolean
  }
  autoCreated: number
  createdAdSpaces: any[]
}

interface AdSpacesState {
  adSpaces: AdSpace[]
  scanResults: ScanResult | null
  isLoading: boolean
  isScanning: boolean
  isCreating: boolean
  error: string | null
  lastFetch: number | null
}

const initialState: AdSpacesState = {
  adSpaces: [],
  scanResults: null,
  isLoading: false,
  isScanning: false,
  isCreating: false,
  error: null,
  lastFetch: null,
}

// Async thunks
export const fetchAdSpaces = createAsyncThunk(
  'adSpaces/fetchAdSpaces',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/publisher/ad-spaces')
      if (!response.ok) {
        throw new Error('Failed to fetch ad spaces')
      }
      const data = await response.json()
      return data.adSpaces || []
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

export const createAdSpace = createAsyncThunk(
  'adSpaces/createAdSpace',
  async (adSpaceData: Omit<AdSpace, 'id' | 'impressions' | 'clicks' | 'conversions' | 'earnings' | 'ctr' | 'createdAt'>, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/publisher/ad-spaces', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(adSpaceData),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to create ad space')
      }
      
      const data = await response.json()
      return data.adSpace
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

export const updateAdSpace = createAsyncThunk(
  'adSpaces/updateAdSpace',
  async ({ id, updates }: { id: string; updates: Partial<AdSpace> }, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/publisher/ad-spaces/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })
      
      if (!response.ok) {
        throw new Error('Failed to update ad space')
      }
      
      const data = await response.json()
      return { id, updates: data.adSpace }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

export const deleteAdSpace = createAsyncThunk(
  'adSpaces/deleteAdSpace',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/publisher/ad-spaces/${id}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to delete ad space')
      }
      
      return id
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

export const scanWebsite = createAsyncThunk(
  'adSpaces/scanWebsite',
  async ({ url, autoCreate }: { url: string; autoCreate: boolean }, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/publisher/scan-website', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url, autoCreate }),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to scan website')
      }
      
      const data = await response.json()
      return data
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

const adSpacesSlice = createSlice({
  name: 'adSpaces',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    clearScanResults: (state) => {
      state.scanResults = null
    },
    toggleAdSpaceStatus: (state, action: PayloadAction<{ id: string; isActive: boolean }>) => {
      const adSpace = state.adSpaces.find(space => space.id === action.payload.id)
      if (adSpace) {
        adSpace.isActive = action.payload.isActive
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch ad spaces
      .addCase(fetchAdSpaces.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchAdSpaces.fulfilled, (state, action) => {
        state.isLoading = false
        state.adSpaces = action.payload
        state.lastFetch = Date.now()
      })
      .addCase(fetchAdSpaces.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
      // Create ad space
      .addCase(createAdSpace.pending, (state) => {
        state.isCreating = true
        state.error = null
      })
      .addCase(createAdSpace.fulfilled, (state, action) => {
        state.isCreating = false
        state.adSpaces.push(action.payload)
      })
      .addCase(createAdSpace.rejected, (state, action) => {
        state.isCreating = false
        state.error = action.payload as string
      })
      // Update ad space
      .addCase(updateAdSpace.fulfilled, (state, action) => {
        const index = state.adSpaces.findIndex(space => space.id === action.payload.id)
        if (index !== -1) {
          state.adSpaces[index] = { ...state.adSpaces[index], ...action.payload.updates }
        }
      })
      // Delete ad space
      .addCase(deleteAdSpace.fulfilled, (state, action) => {
        state.adSpaces = state.adSpaces.filter(space => space.id !== action.payload)
      })
      // Scan website
      .addCase(scanWebsite.pending, (state) => {
        state.isScanning = true
        state.error = null
      })
      .addCase(scanWebsite.fulfilled, (state, action) => {
        state.isScanning = false
        state.scanResults = action.payload
        // If auto-created, add the new ad spaces to the list
        if (action.payload.autoCreated > 0) {
          state.adSpaces.push(...action.payload.createdAdSpaces)
        }
      })
      .addCase(scanWebsite.rejected, (state, action) => {
        state.isScanning = false
        state.error = action.payload as string
      })
  },
})

export const { clearError, clearScanResults, toggleAdSpaceStatus } = adSpacesSlice.actions
export default adSpacesSlice.reducer
