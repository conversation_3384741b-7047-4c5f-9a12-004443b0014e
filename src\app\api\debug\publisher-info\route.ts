import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// GET /api/debug/publisher-info - Debug endpoint to check publisher data
export async function GET(request: NextRequest) {
  try {
    // Get all publishers with their API keys and ad spaces
    const publishers = await prisma.publisherProfile.findMany({
      select: {
        id: true,
        websiteName: true,
        apiKey: true,
        isActive: true,
        user: {
          select: {
            email: true,
            name: true
          }
        },
        adSpaces: {
          select: {
            id: true,
            name: true,
            format: true,
            width: true,
            height: true,
            position: true,
            isActive: true
          }
        }
      }
    })

    // Get all campaigns for context
    const campaigns = await prisma.campaign.findMany({
      select: {
        id: true,
        name: true,
        status: true,
        budget: true,
        bidAmount: true,
        advertiser: {
          select: {
            companyName: true,
            user: {
              select: {
                email: true
              }
            }
          }
        },
        ads: {
          select: {
            id: true,
            title: true,
            format: true,
            width: true,
            height: true,
            isActive: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        publishers,
        campaigns,
        totalPublishers: publishers.length,
        totalCampaigns: campaigns.length,
        activeCampaigns: campaigns.filter(c => c.status === 'ACTIVE').length
      },
      instructions: {
        step1: "Copy an API key from the publishers list",
        step2: "Copy an Ad Space ID from the adSpaces array",
        step3: "Use these in your HTML test file",
        step4: "Make sure there are active campaigns with ads"
      }
    })

  } catch (error) {
    console.error("Debug endpoint error:", error)
    return NextResponse.json(
      { 
        success: false,
        error: "Failed to fetch debug info",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    )
  }
}

// POST /api/debug/publisher-info - Test API key validation
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { apiKey, adSpaceId } = body

    if (!apiKey) {
      return NextResponse.json({
        success: false,
        error: "API key is required"
      }, { status: 400 })
    }

    // Check if API key exists
    const publisher = await prisma.publisherProfile.findUnique({
      where: { apiKey },
      include: {
        user: {
          select: {
            email: true,
            name: true
          }
        },
        adSpaces: {
          where: adSpaceId ? { id: adSpaceId } : undefined,
          select: {
            id: true,
            name: true,
            format: true,
            width: true,
            height: true,
            position: true,
            isActive: true
          }
        }
      }
    })

    if (!publisher) {
      return NextResponse.json({
        success: false,
        error: "Invalid API key",
        apiKey: apiKey.substring(0, 10) + "..."
      }, { status: 401 })
    }

    const adSpace = adSpaceId ? publisher.adSpaces.find(space => space.id === adSpaceId) : null

    return NextResponse.json({
      success: true,
      message: "API key is valid!",
      publisher: {
        id: publisher.id,
        websiteName: publisher.websiteName,
        email: publisher.user.email,
        isActive: publisher.isActive
      },
      adSpace: adSpace ? {
        id: adSpace.id,
        name: adSpace.name,
        format: adSpace.format,
        dimensions: `${adSpace.width}x${adSpace.height}`,
        position: adSpace.position,
        isActive: adSpace.isActive
      } : null,
      availableAdSpaces: publisher.adSpaces.map(space => ({
        id: space.id,
        name: space.name,
        format: space.format,
        dimensions: `${space.width}x${space.height}`
      }))
    })

  } catch (error) {
    console.error("API key validation error:", error)
    return NextResponse.json(
      { 
        success: false,
        error: "Failed to validate API key",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    )
  }
}
