"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { 
  Shield, 
  MousePointer, 
  DollarSign, 
  AlertTriangle, 
  CheckCircle,
  Eye,
  TrendingUp,
  Clock
} from "lucide-react"

interface ClickEvent {
  id: string
  timestamp: string
  campaignId: string
  adId: string
  adSpaceId: string
  ipAddress: string
  isFraudulent: boolean
  fraudReason?: string
  publisherRevenue: number
  platformRevenue: number
  deviceType: string
  browser: string
  country: string
}

export default function ClickTrackingMonitor() {
  const [recentClicks, setRecentClicks] = useState<ClickEvent[]>([])
  const [stats, setStats] = useState({
    totalClicks: 0,
    validClicks: 0,
    fraudulentClicks: 0,
    totalRevenue: 0,
    fraudRate: 0
  })
  const [isMonitoring, setIsMonitoring] = useState(false)

  useEffect(() => {
    if (isMonitoring) {
      const interval = setInterval(fetchRecentClicks, 2000) // Every 2 seconds
      return () => clearInterval(interval)
    }
  }, [isMonitoring])

  const fetchRecentClicks = async () => {
    try {
      // This would be a real API endpoint in production
      // For now, we'll simulate click data
      const mockClick: ClickEvent = {
        id: `click_${Date.now()}`,
        timestamp: new Date().toISOString(),
        campaignId: "campaign_123",
        adId: "ad_456",
        adSpaceId: "space_789",
        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
        isFraudulent: Math.random() < 0.2, // 20% fraud rate for demo
        fraudReason: Math.random() < 0.2 ? "Rapid clicking detected" : undefined,
        publisherRevenue: Math.random() * 2,
        platformRevenue: Math.random() * 1,
        deviceType: ["desktop", "mobile", "tablet"][Math.floor(Math.random() * 3)],
        browser: ["Chrome", "Firefox", "Safari", "Edge"][Math.floor(Math.random() * 4)],
        country: ["India", "USA", "UK", "Canada"][Math.floor(Math.random() * 4)]
      }

      setRecentClicks(prev => [mockClick, ...prev.slice(0, 9)]) // Keep last 10 clicks

      // Update stats
      setStats(prev => ({
        totalClicks: prev.totalClicks + 1,
        validClicks: prev.validClicks + (mockClick.isFraudulent ? 0 : 1),
        fraudulentClicks: prev.fraudulentClicks + (mockClick.isFraudulent ? 1 : 0),
        totalRevenue: prev.totalRevenue + (mockClick.isFraudulent ? 0 : mockClick.publisherRevenue + mockClick.platformRevenue),
        fraudRate: ((prev.fraudulentClicks + (mockClick.isFraudulent ? 1 : 0)) / (prev.totalClicks + 1)) * 100
      }))
    } catch (error) {
      console.error("Error fetching clicks:", error)
    }
  }

  const resetStats = () => {
    setRecentClicks([])
    setStats({
      totalClicks: 0,
      validClicks: 0,
      fraudulentClicks: 0,
      totalRevenue: 0,
      fraudRate: 0
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Enhanced Click Tracking Monitor</h2>
          <p className="text-muted-foreground">
            Real-time monitoring of click fraud detection and revenue tracking
          </p>
        </div>
        <div className="flex space-x-2">
          <Button 
            onClick={() => setIsMonitoring(!isMonitoring)}
            variant={isMonitoring ? "destructive" : "default"}
          >
            {isMonitoring ? "Stop Monitoring" : "Start Monitoring"}
          </Button>
          <Button onClick={resetStats} variant="outline">
            Reset Stats
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
            <MousePointer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalClicks}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Valid Clicks</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.validClicks}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fraudulent</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.fraudulentClicks}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fraud Rate</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.fraudRate.toFixed(1)}%
            </div>
            <Progress 
              value={stats.fraudRate} 
              className="mt-2"
              // @ts-ignore
              indicatorClassName={stats.fraudRate > 30 ? "bg-red-500" : stats.fraudRate > 15 ? "bg-yellow-500" : "bg-green-500"}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{stats.totalRevenue.toFixed(2)}</div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Clicks */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Eye className="h-5 w-5 mr-2" />
            Recent Click Events
          </CardTitle>
          <CardDescription>
            Live stream of click tracking events with fraud detection results
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recentClicks.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {isMonitoring ? "Waiting for click events..." : "Start monitoring to see click events"}
            </div>
          ) : (
            <div className="space-y-3">
              {recentClicks.map((click) => (
                <div 
                  key={click.id}
                  className={`p-4 rounded-lg border ${
                    click.isFraudulent 
                      ? "border-red-200 bg-red-50" 
                      : "border-green-200 bg-green-50"
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <Badge 
                        variant={click.isFraudulent ? "destructive" : "default"}
                        className={click.isFraudulent ? "bg-red-500" : "bg-green-500"}
                      >
                        {click.isFraudulent ? (
                          <>
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            FRAUD
                          </>
                        ) : (
                          <>
                            <CheckCircle className="h-3 w-3 mr-1" />
                            VALID
                          </>
                        )}
                      </Badge>
                      <Badge variant="outline">{click.deviceType}</Badge>
                      <Badge variant="outline">{click.browser}</Badge>
                      <Badge variant="outline">{click.country}</Badge>
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Clock className="h-4 w-4 mr-1" />
                      {new Date(click.timestamp).toLocaleTimeString()}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium">IP Address:</span>
                      <div className="text-muted-foreground">{click.ipAddress}</div>
                    </div>
                    <div>
                      <span className="font-medium">Campaign:</span>
                      <div className="text-muted-foreground">{click.campaignId}</div>
                    </div>
                    <div>
                      <span className="font-medium">Revenue Split:</span>
                      <div className="text-muted-foreground">
                        {click.isFraudulent ? (
                          "₹0.00 (Blocked)"
                        ) : (
                          `₹${(click.publisherRevenue + click.platformRevenue).toFixed(2)}`
                        )}
                      </div>
                    </div>
                    <div>
                      <span className="font-medium">Status:</span>
                      <div className={click.isFraudulent ? "text-red-600" : "text-green-600"}>
                        {click.isFraudulent ? click.fraudReason : "Valid Click"}
                      </div>
                    </div>
                  </div>

                  {!click.isFraudulent && (
                    <div className="mt-2 pt-2 border-t border-gray-200">
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Publisher Revenue: ₹{click.publisherRevenue.toFixed(2)}</span>
                        <span>Platform Revenue: ₹{click.platformRevenue.toFixed(2)}</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Fraud Detection Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="h-5 w-5 mr-2" />
            Fraud Detection Features
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Active Protection</h4>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• IP-based click frequency limiting</li>
                <li>• Rapid clicking detection (30s cooldown)</li>
                <li>• Bot and crawler identification</li>
                <li>• Click farm behavior analysis</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Revenue Protection</h4>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• Dynamic revenue splitting</li>
                <li>• Performance-based publisher rates</li>
                <li>• Quality score adjustments</li>
                <li>• Real-time budget deduction</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
