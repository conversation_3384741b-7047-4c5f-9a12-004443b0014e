"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Switch } from "@/components/ui/switch"
import { AdvertiserLayout } from "@/components/dashboard/advertiser-layout"
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye, 
  MousePointer, 
  DollarSign, 
  Play,
  Pause,
  Copy,
  MoreHorizontal,
  TrendingUp,
  Calendar
} from "lucide-react"
import { toast } from "sonner"
import { CampaignStatus, PricingModel, Campaign } from "@/lib/types"

export default function CampaignsPage() {
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [filteredCampaigns, setFilteredCampaigns] = useState<Campaign[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")

  useEffect(() => {
    fetchCampaigns()
    // Also check for expired campaigns and update them
    updateExpiredCampaigns()
  }, [])

  const updateExpiredCampaigns = async () => {
    try {
      const response = await fetch("/api/admin/campaigns/update-expired", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (response.ok) {
        const data = await response.json()
        if (data.totalUpdated > 0) {
          console.log(`Updated ${data.totalUpdated} expired campaigns`)
          // Refresh campaigns list to show updated statuses
          fetchCampaigns()
        }
      }
    } catch (error) {
      console.error("Failed to update expired campaigns:", error)
      // Don't show error to user as this is a background operation
    }
  }

  useEffect(() => {
    filterCampaigns()
  }, [campaigns, searchTerm, statusFilter])

  const fetchCampaigns = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("/api/advertiser/campaigns")

      if (!response.ok) {
        throw new Error("Failed to fetch campaigns")
      }

      const data = await response.json()
      setCampaigns(data.campaigns || [])
    } catch (error) {
      console.error("Failed to fetch campaigns:", error)
      toast.error("Failed to load campaigns")
    } finally {
      setIsLoading(false)
    }
  }

  const filterCampaigns = () => {
    let filtered = campaigns

    if (searchTerm) {
      filtered = filtered.filter(campaign =>
        campaign.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter(campaign => campaign.status === statusFilter)
    }

    setFilteredCampaigns(filtered)
  }

  const toggleCampaignStatus = async (id: string, currentStatus: CampaignStatus) => {
    try {
      const newStatus = currentStatus === "ACTIVE" ? "PAUSED" : "ACTIVE"

      // Make API call to update campaign status
      const response = await fetch(`/api/advertiser/campaigns/${id}/status`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to update campaign status")
      }

      const data = await response.json()

      // Update local state with the response data
      setCampaigns(campaigns.map(campaign =>
        campaign.id === id ? { ...campaign, status: data.status || newStatus } : campaign
      ))

      toast.success(`Campaign ${newStatus.toLowerCase()} successfully!`)
    } catch (error) {
      console.error("Failed to update campaign:", error)
      toast.error(error instanceof Error ? error.message : "Failed to update campaign")
    }
  }

  const duplicateCampaign = async (campaign: Campaign) => {
    try {
      const duplicated: Campaign = {
        ...campaign,
        id: Date.now().toString(),
        name: `${campaign.name} (Copy)`,
        status: "DRAFT",
        spent: 0,
        impressions: 0,
        clicks: 0,
        conversions: 0,
        createdAt: new Date(),
      }
      setCampaigns([duplicated, ...campaigns])
      toast.success("Campaign duplicated successfully!")
    } catch (error) {
      console.error("Failed to duplicate campaign:", error)
      toast.error("Failed to duplicate campaign")
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US").format(num)
  }

  const getStatusBadge = (status: CampaignStatus) => {
    switch (status) {
      case "ACTIVE":
        return <Badge className="bg-green-100 text-green-800"><Play className="w-3 h-3 mr-1" />Active</Badge>
      case "PAUSED":
        return <Badge className="bg-yellow-100 text-yellow-800"><Pause className="w-3 h-3 mr-1" />Paused</Badge>
      case "DRAFT":
        return <Badge variant="secondary">Draft</Badge>
      case "COMPLETED":
        return <Badge className="bg-blue-100 text-blue-800">Completed</Badge>
      case "CANCELLED":
        return <Badge variant="destructive">Cancelled</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getPricingModelBadge = (model: PricingModel) => {
    const colors = {
      CPC: "bg-blue-100 text-blue-800",
      CPM: "bg-purple-100 text-purple-800",
      CPA: "bg-green-100 text-green-800",
    }
    return <Badge className={colors[model]}>{model}</Badge>
  }

  return (
    <AdvertiserLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Campaigns</h2>
            <p className="text-muted-foreground">
              Create and manage your advertising campaigns
            </p>
          </div>
          
          <Link href="/dashboard/advertiser/campaigns/create">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Campaign
            </Button>
          </Link>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search campaigns..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="PAUSED">Paused</SelectItem>
                  <SelectItem value="DRAFT">Draft</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Campaigns Table */}
        <Card>
          <CardHeader>
            <CardTitle>Your Campaigns</CardTitle>
            <CardDescription>
              Manage all your advertising campaigns in one place
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Campaign</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Budget</TableHead>
                  <TableHead>Spent</TableHead>
                  <TableHead>Impressions</TableHead>
                  <TableHead>Clicks</TableHead>
                  <TableHead>CTR</TableHead>
                  <TableHead>Conversions</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCampaigns.map((campaign) => (
                  <TableRow key={campaign.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{campaign.name}</div>
                        <div className="flex items-center gap-2 mt-1">
                          {getPricingModelBadge(campaign.pricingModel)}
                          <span className="text-xs text-muted-foreground">
                            {campaign.targetRegions.join(", ")}
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(campaign.status)}
                        {(campaign.status === "ACTIVE" || campaign.status === "PAUSED") && (
                          <Switch
                            checked={campaign.status === "ACTIVE"}
                            onCheckedChange={() => toggleCampaignStatus(campaign.id, campaign.status)}
                          />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{formatCurrency(campaign.budget)}</div>
                        {campaign.dailyBudget && (
                          <div className="text-xs text-muted-foreground">
                            {formatCurrency(campaign.dailyBudget)}/day
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{formatCurrency(campaign.spent || 0)}</div>
                        <div className="text-xs text-muted-foreground">
                          {(((campaign.spent || 0) / campaign.budget) * 100).toFixed(1)}% used
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{formatNumber(campaign.impressions || 0)}</TableCell>
                    <TableCell>{formatNumber(campaign.clicks || 0)}</TableCell>
                    <TableCell>{(campaign.ctr || 0).toFixed(2)}%</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{campaign.conversions || 0}</div>
                        <div className="text-xs text-muted-foreground">
                          {formatCurrency(campaign.costPerClick || 0)} CPC
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Link href={`/dashboard/advertiser/campaigns/${campaign.id}`}>
                          <Button variant="outline" size="icon" title="View details">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Link href={`/dashboard/advertiser/campaigns/${campaign.id}/edit`}>
                          <Button variant="outline" size="icon" title="Edit">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => duplicateCampaign(campaign)}
                          title="Duplicate"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Summary Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{campaigns.length}</div>
              <p className="text-xs text-muted-foreground">
                {campaigns.filter(c => c.status === "ACTIVE").length} active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(campaigns.reduce((sum, c) => sum + (c.spent || 0), 0))}
              </div>
              <p className="text-xs text-muted-foreground">
                Across all campaigns
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Impressions</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatNumber(campaigns.reduce((sum, c) => sum + (c.impressions || 0), 0))}
              </div>
              <p className="text-xs text-muted-foreground">
                Total reach
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Conversions</CardTitle>
              <MousePointer className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {campaigns.reduce((sum, c) => sum + (c.conversions || 0), 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                Total conversions
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdvertiserLayout>
  )
}
