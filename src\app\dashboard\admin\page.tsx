"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Users, 
  Target, 
  TrendingUp, 
  DollarSign,
  Activity,
  Shield,
  BarChart3,
  Settings,
  AlertTriangle,
  CheckCircle
} from "lucide-react"
import Link from "next/link"
import { toast } from "sonner"
import { AdminLayout } from "@/components/dashboard/admin-layout"

interface AdminStats {
  totalPublishers: number
  activePublishers: number
  totalAdvertisers: number
  activeAdvertisers: number
  totalCampaigns: number
  activeCampaigns: number
  totalRevenue: number
  totalImpressions: number
  totalClicks: number
  ctr: number
  fillRate: number
}

interface SystemHealth {
  databaseConnected: boolean
  biddingEngineStatus: string
  trackingSystemStatus: string
  fraudDetectionStatus: string
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats>({
    totalPublishers: 0,
    activePublishers: 0,
    totalAdvertisers: 0,
    activeAdvertisers: 0,
    totalCampaigns: 0,
    activeCampaigns: 0,
    totalRevenue: 0,
    totalImpressions: 0,
    totalClicks: 0,
    ctr: 0,
    fillRate: 0
  })
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    databaseConnected: false,
    biddingEngineStatus: "unknown",
    trackingSystemStatus: "unknown",
    fraudDetectionStatus: "unknown"
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
    
    // Refresh data every 30 seconds
    const interval = setInterval(fetchDashboardData, 30000)
    return () => clearInterval(interval)
  }, [])

  const fetchDashboardData = async () => {
    try {
      const response = await fetch("/api/admin/dashboard")
      
      if (response.ok) {
        const data = await response.json()
        setStats(data.stats)
        setSystemHealth(data.systemHealth)
      } else {
        toast.error("Failed to load admin dashboard data")
      }
    } catch (error) {
      console.error("Failed to fetch admin dashboard data:", error)
      toast.error("Failed to load dashboard data")
    } finally {
      setIsLoading(false)
    }
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US").format(num)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading admin dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            System overview and management
          </p>
        </div>
        <div className="flex gap-2">
          <Link href="/dashboard/admin/bidding">
            <Button variant="outline">
              <BarChart3 className="mr-2 h-4 w-4" />
              Bidding Analytics
            </Button>
          </Link>
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* System Health */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Database</CardTitle>
            {systemHealth.databaseConnected ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
          </CardHeader>
          <CardContent>
            <Badge className={systemHealth.databaseConnected ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
              {systemHealth.databaseConnected ? "Connected" : "Disconnected"}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bidding Engine</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className="bg-green-100 text-green-800">
              {systemHealth.biddingEngineStatus}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tracking System</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className="bg-green-100 text-green-800">
              {systemHealth.trackingSystemStatus}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fraud Detection</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className="bg-green-100 text-green-800">
              {systemHealth.fraudDetectionStatus}
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              Platform revenue
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats.activeCampaigns)}</div>
            <p className="text-xs text-muted-foreground">
              of {formatNumber(stats.totalCampaigns)} total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Publishers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats.activePublishers)}</div>
            <p className="text-xs text-muted-foreground">
              of {formatNumber(stats.totalPublishers)} total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Advertisers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats.activeAdvertisers)}</div>
            <p className="text-xs text-muted-foreground">
              of {formatNumber(stats.totalAdvertisers)} total
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Impressions</CardTitle>
            <CardDescription>Total ad impressions served</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{formatNumber(stats.totalImpressions)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Click-Through Rate</CardTitle>
            <CardDescription>Average CTR across all campaigns</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{stats.ctr.toFixed(2)}%</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Fill Rate</CardTitle>
            <CardDescription>Percentage of ad requests filled</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{stats.fillRate.toFixed(1)}%</div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common administrative tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-2 md:grid-cols-3">
            <Link href="/dashboard/admin/bidding">
              <Button className="w-full" variant="outline">
                <BarChart3 className="mr-2 h-4 w-4" />
                View Bidding Analytics
              </Button>
            </Link>
            <Button className="w-full" variant="outline" disabled>
              <Users className="mr-2 h-4 w-4" />
              Manage Users
            </Button>
            <Button className="w-full" variant="outline" disabled>
              <Settings className="mr-2 h-4 w-4" />
              System Settings
            </Button>
          </div>
        </CardContent>
      </Card>
      </div>
    </AdminLayout>
  )
}
