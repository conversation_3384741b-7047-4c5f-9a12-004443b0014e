"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { AdvertiserLayout } from "@/components/dashboard/advertiser-layout"
import { 
  Wallet, 
  CreditCard, 
  Plus, 
  ArrowUpRight, 
  ArrowDownRight,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw
} from "lucide-react"
import { toast } from "sonner"
import { WalletTransaction, PaymentOrder } from "@/lib/types"

declare global {
  interface Window {
    Razorpay: any
  }
}

export default function BillingPage() {
  const { data: session } = useSession()
  const [balance, setBalance] = useState(0)
  const [transactions, setTransactions] = useState<WalletTransaction[]>([])
  const [paymentOrders, setPaymentOrders] = useState<PaymentOrder[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isAddingFunds, setIsAddingFunds] = useState(false)
  const [addAmount, setAddAmount] = useState("")

  useEffect(() => {
    fetchWalletData()
    loadRazorpayScript()
  }, [])

  const loadRazorpayScript = () => {
    const script = document.createElement("script")
    script.src = "https://checkout.razorpay.com/v1/checkout.js"
    script.async = true
    document.body.appendChild(script)
  }

  const fetchWalletData = async () => {
    try {
      setIsLoading(true)
      
      // Fetch current balance and transactions
      const response = await fetch("/api/advertiser/wallet")
      
      if (!response.ok) {
        throw new Error("Failed to fetch wallet data")
      }
      
      const data = await response.json()
      setBalance(data.balance || 0)
      setTransactions(data.transactions || [])
      setPaymentOrders(data.paymentOrders || [])
    } catch (error) {
      console.error("Failed to fetch wallet data:", error)
      toast.error("Failed to load wallet data")
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddFunds = async () => {
    try {
      const amount = parseFloat(addAmount)
      
      if (isNaN(amount) || amount < 100) {
        toast.error("Please enter a valid amount (minimum ₹100)")
        return
      }

      if (amount > 100000) {
        toast.error("Maximum amount allowed is ₹1,00,000")
        return
      }

      setIsAddingFunds(true)

      // Create payment order
      const orderResponse = await fetch("/api/advertiser/payments/create-order", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount,
          currency: "INR",
        }),
      })

      if (!orderResponse.ok) {
        const errorData = await orderResponse.json()
        throw new Error(errorData.message || "Failed to create payment order")
      }

      const orderData = await orderResponse.json()

      // Initialize Razorpay payment
      const options = {
        key: orderData.order.key,
        amount: orderData.razorpayOrder.amount,
        currency: orderData.razorpayOrder.currency,
        name: "Ad Network",
        description: "Wallet Top-up",
        order_id: orderData.razorpayOrder.id,
        handler: async (response: any) => {
          await verifyPayment(response, orderData.order.id)
        },
        prefill: {
          email: session?.user?.email,
          name: session?.user?.name,
        },
        theme: {
          color: "#3B82F6",
        },
        modal: {
          ondismiss: () => {
            setIsAddingFunds(false)
            toast.error("Payment cancelled")
          },
        },
      }

      const razorpay = new window.Razorpay(options)
      razorpay.open()
    } catch (error) {
      console.error("Payment initiation error:", error)
      toast.error(error instanceof Error ? error.message : "Failed to initiate payment")
      setIsAddingFunds(false)
    }
  }

  const verifyPayment = async (razorpayResponse: any, orderId: string) => {
    try {
      const verifyResponse = await fetch("/api/advertiser/payments/verify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          razorpay_order_id: razorpayResponse.razorpay_order_id,
          razorpay_payment_id: razorpayResponse.razorpay_payment_id,
          razorpay_signature: razorpayResponse.razorpay_signature,
          orderId,
        }),
      })

      if (!verifyResponse.ok) {
        const errorData = await verifyResponse.json()
        throw new Error(errorData.message || "Payment verification failed")
      }

      const verifyData = await verifyResponse.json()
      
      toast.success(`Payment successful! ₹${verifyData.amountAdded} added to your wallet`)
      setAddAmount("")
      await fetchWalletData() // Refresh wallet data
    } catch (error) {
      console.error("Payment verification error:", error)
      toast.error(error instanceof Error ? error.message : "Payment verification failed")
    } finally {
      setIsAddingFunds(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
    }).format(amount)
  }

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString("en-IN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getTransactionIcon = (type: string) => {
    return type === "CREDIT" ? (
      <ArrowUpRight className="h-4 w-4 text-green-600" />
    ) : (
      <ArrowDownRight className="h-4 w-4 text-red-600" />
    )
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "PAID":
      case "SUCCESS":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "FAILED":
      case "CANCELLED":
        return <XCircle className="h-4 w-4 text-red-600" />
      case "CREATED":
      case "PENDING":
        return <Clock className="h-4 w-4 text-yellow-600" />
      default:
        return <RefreshCw className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PAID":
      case "SUCCESS":
        return "bg-green-100 text-green-800"
      case "FAILED":
      case "CANCELLED":
        return "bg-red-100 text-red-800"
      case "CREATED":
      case "PENDING":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  if (isLoading) {
    return (
      <AdvertiserLayout>
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      </AdvertiserLayout>
    )
  }

  return (
    <AdvertiserLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Billing & Wallet</h1>
          <p className="text-muted-foreground">
            Manage your account balance and payment history
          </p>
        </div>

        {/* Current Balance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wallet className="h-5 w-5" />
              Current Balance
            </CardTitle>
            <CardDescription>
              Available funds for your advertising campaigns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-green-600">
                  {formatCurrency(balance)}
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Available for campaigns
                </p>
              </div>
              <Button onClick={() => fetchWalletData()} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Add Funds */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Add Funds
            </CardTitle>
            <CardDescription>
              Top up your wallet to start or continue your campaigns
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <Label htmlFor="amount">Amount (₹)</Label>
                <Input
                  id="amount"
                  type="number"
                  placeholder="Enter amount (min ₹100)"
                  value={addAmount}
                  onChange={(e) => setAddAmount(e.target.value)}
                  min="100"
                  max="100000"
                />
              </div>
              <div className="flex items-end">
                <Button
                  onClick={handleAddFunds}
                  disabled={isAddingFunds || !addAmount}
                  className="flex items-center gap-2"
                >
                  <CreditCard className="h-4 w-4" />
                  {isAddingFunds ? "Processing..." : "Add Funds"}
                </Button>
              </div>
            </div>
            <div className="flex gap-2">
              {[500, 1000, 2000, 5000].map((amount) => (
                <Button
                  key={amount}
                  variant="outline"
                  size="sm"
                  onClick={() => setAddAmount(amount.toString())}
                >
                  ₹{amount}
                </Button>
              ))}
            </div>
            <p className="text-xs text-muted-foreground">
              Secure payments powered by Razorpay. Minimum ₹100, Maximum ₹1,00,000 per transaction.
            </p>
          </CardContent>
        </Card>

        {/* Transaction History */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Wallet Transactions */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
              <CardDescription>
                Your wallet transaction history
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {transactions.length === 0 ? (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No transactions yet
                  </p>
                ) : (
                  transactions.slice(0, 10).map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {getTransactionIcon(transaction.type)}
                        <div>
                          <p className="text-sm font-medium">{transaction.description}</p>
                          <p className="text-xs text-muted-foreground">
                            {formatDate(transaction.createdAt)}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`text-sm font-medium ${
                          transaction.type === "CREDIT" ? "text-green-600" : "text-red-600"
                        }`}>
                          {transaction.type === "CREDIT" ? "+" : "-"}{formatCurrency(transaction.amount)}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Balance: {formatCurrency(transaction.balanceAfter)}
                        </p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          {/* Payment Orders */}
          <Card>
            <CardHeader>
              <CardTitle>Payment History</CardTitle>
              <CardDescription>
                Your payment order history
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {paymentOrders.length === 0 ? (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No payments yet
                  </p>
                ) : (
                  paymentOrders.slice(0, 10).map((order) => (
                    <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(order.status)}
                        <div>
                          <p className="text-sm font-medium">
                            Payment Order #{order.razorpayOrderId.slice(-8)}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {formatDate(order.createdAt)}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          {formatCurrency(order.amount)}
                        </p>
                        <Badge className={getStatusColor(order.status)} variant="secondary">
                          {order.status}
                        </Badge>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdvertiserLayout>
  )
}
