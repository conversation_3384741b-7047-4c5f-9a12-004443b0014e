import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// GET /api/advertiser/wallet - Get wallet data including balance, transactions, and payment orders
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get advertiser profile
    const advertiserProfile = await prisma.advertiserProfile.findUnique({
      where: { userId: session.user.id },
    })

    if (!advertiserProfile) {
      return NextResponse.json(
        { message: "Advertiser profile not found" },
        { status: 404 }
      )
    }

    // Get wallet transactions (last 50)
    const walletTransactions = await prisma.walletTransaction.findMany({
      where: { advertiserId: advertiserProfile.id },
      orderBy: { createdAt: "desc" },
      take: 50,
      include: {
        relatedOrder: {
          select: {
            id: true,
            razorpayOrderId: true,
            status: true,
          },
        },
        relatedCampaign: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    // Get payment orders (last 20)
    const paymentOrders = await prisma.paymentOrder.findMany({
      where: { advertiserId: advertiserProfile.id },
      orderBy: { createdAt: "desc" },
      take: 20,
      include: {
        transactions: {
          select: {
            id: true,
            razorpayPaymentId: true,
            status: true,
            failureReason: true,
            createdAt: true,
          },
        },
      },
    })

    // Get payment transactions (last 20)
    const paymentTransactions = await prisma.paymentTransaction.findMany({
      where: { advertiserId: advertiserProfile.id },
      orderBy: { createdAt: "desc" },
      take: 20,
      include: {
        order: {
          select: {
            id: true,
            razorpayOrderId: true,
            amount: true,
            currency: true,
          },
        },
      },
    })

    // Calculate wallet statistics
    const totalCredits = await prisma.walletTransaction.aggregate({
      where: {
        advertiserId: advertiserProfile.id,
        type: "CREDIT",
      },
      _sum: {
        amount: true,
      },
    })

    const totalDebits = await prisma.walletTransaction.aggregate({
      where: {
        advertiserId: advertiserProfile.id,
        type: "DEBIT",
      },
      _sum: {
        amount: true,
      },
    })

    const totalSpent = await prisma.paymentTransaction.aggregate({
      where: {
        advertiserId: advertiserProfile.id,
        status: "SUCCESS",
      },
      _sum: {
        amount: true,
      },
    })

    // Calculate monthly spending (current month)
    const currentMonth = new Date()
    currentMonth.setDate(1)
    currentMonth.setHours(0, 0, 0, 0)

    const monthlySpending = await prisma.walletTransaction.aggregate({
      where: {
        advertiserId: advertiserProfile.id,
        type: "DEBIT",
        createdAt: {
          gte: currentMonth,
        },
      },
      _sum: {
        amount: true,
      },
    })

    return NextResponse.json({
      balance: advertiserProfile.balance,
      transactions: walletTransactions.map((transaction) => ({
        id: transaction.id,
        type: transaction.type,
        amount: transaction.amount,
        description: transaction.description,
        balanceAfter: transaction.balanceAfter,
        createdAt: transaction.createdAt,
        relatedOrder: transaction.relatedOrder,
        relatedCampaign: transaction.relatedCampaign,
      })),
      paymentOrders: paymentOrders.map((order) => ({
        id: order.id,
        amount: order.amount,
        currency: order.currency,
        razorpayOrderId: order.razorpayOrderId,
        status: order.status,
        createdAt: order.createdAt,
        updatedAt: order.updatedAt,
        transactions: order.transactions,
      })),
      paymentTransactions: paymentTransactions.map((transaction) => ({
        id: transaction.id,
        amount: transaction.amount,
        currency: transaction.currency,
        status: transaction.status,
        razorpayPaymentId: transaction.razorpayPaymentId,
        failureReason: transaction.failureReason,
        createdAt: transaction.createdAt,
        order: transaction.order,
      })),
      statistics: {
        totalCredits: totalCredits._sum.amount || 0,
        totalDebits: totalDebits._sum.amount || 0,
        totalSpent: totalSpent._sum.amount || 0,
        monthlySpending: monthlySpending._sum.amount || 0,
        currentBalance: advertiserProfile.balance,
      },
    })
  } catch (error) {
    console.error("Wallet data fetch error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
