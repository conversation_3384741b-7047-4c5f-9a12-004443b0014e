import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { BiddingEngine, BidRequest } from "@/lib/bidding/bidding-engine"
import { z } from "zod"
import { headers } from "next/headers"
import { apiKeyLimiter, getClientIP } from "@/lib/rate-limiter"
import { initializeServices } from "@/lib/init-services"
import { getFallbackAd } from "@/lib/tracking-utils"
import { FrequencyManager } from "@/lib/frequency/frequency-manager"

const adRequestSchema = z.object({
  adSpaceId: z.string().min(1, "Ad space ID is required"),
  apiKey: z.string().min(1, "API key is required"),
  format: z.enum(["BANNER", "VIDEO", "NATIVE", "POPUP"]),
  width: z.number().min(1, "Width must be positive"),
  height: z.number().min(1, "Height must be positive"),
  position: z.string().optional(),
  userAgent: z.string().optional(),
  referrer: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    // Initialize services if not already done
    initializeServices()

    const body = await request.json()
    const validatedData = adRequestSchema.parse(body)

    // Rate limiting by API key
    const apiKeyLimit = await apiKeyLimiter.checkLimit(validatedData.apiKey)
    if (!apiKeyLimit.allowed) {
      return NextResponse.json(
        {
          error: "Rate limit exceeded",
          resetTime: apiKeyLimit.resetTime,
          remaining: apiKeyLimit.remaining
        },
        { status: 429 }
      )
    }
    
    // Get client IP and headers
    const headersList = await headers()
    const ipAddress = getClientIP(request)
    const userAgent = headersList.get("user-agent") || validatedData.userAgent || ""
    const referrer = headersList.get("referer") || validatedData.referrer || ""

    // 1. Validate API key and get publisher info
    const publisherProfile = await prisma.publisherProfile.findUnique({
      where: { apiKey: validatedData.apiKey },
      include: {
        adSpaces: {
          where: { id: validatedData.adSpaceId },
        },
      },
    })

    if (!publisherProfile) {
      return NextResponse.json(
        { error: "Invalid API key" },
        { status: 401 }
      )
    }

    const adSpace = publisherProfile.adSpaces[0]
    if (!adSpace) {
      return NextResponse.json(
        { error: "Ad space not found" },
        { status: 404 }
      )
    }

    if (!adSpace.isActive) {
      return NextResponse.json(
        { error: "Ad space is not active" },
        { status: 400 }
      )
    }

    // 2. Validate ad space dimensions and format
    if (adSpace.format !== validatedData.format ||
        adSpace.width !== validatedData.width ||
        adSpace.height !== validatedData.height) {
      return NextResponse.json(
        { error: "Ad space dimensions or format mismatch" },
        { status: 400 }
      )
    }

    // 3. Create bid request
    const bidRequest: BidRequest = {
      adSpaceId: validatedData.adSpaceId,
      publisherId: publisherProfile.id,
      format: validatedData.format,
      width: validatedData.width,
      height: validatedData.height,
      position: validatedData.position || adSpace.position,
      publisherRegion: publisherProfile.region,
      publisherCategory: publisherProfile.category,
      userAgent,
      ipAddress,
      referrer,
      timestamp: new Date(),
    }

    // 4. Process bid request through bidding engine
    const biddingEngine = BiddingEngine.getInstance()
    const bidResponse = await biddingEngine.processBidRequest(bidRequest)

    // 5. Check frequency cap if we have a winning ad
    if (bidResponse.success && bidResponse.ad && bidResponse.campaignId) {
      const frequencyManager = FrequencyManager.getInstance()
      const frequencyCheck = await frequencyManager.checkFrequency({
        campaignId: bidResponse.campaignId,
        adId: bidResponse.ad.id,
        adSpaceId: validatedData.adSpaceId,
        ipAddress,
        userAgent,
        deviceType: getDeviceType(userAgent)
      })

      if (!frequencyCheck.allowed) {
        console.log(`🚫 Frequency cap exceeded: ${frequencyCheck.reason}`)

        // Try to get a fallback ad or return no ad
        const fallbackAd = await getFallbackAd(validatedData.adSpaceId)
        if (fallbackAd) {
          return NextResponse.json({
            success: true,
            ad: fallbackAd,
            tracking: {
              impressionUrl: `/api/tracking/impression?id=fallback_${Date.now()}`,
              clickUrl: `/api/tracking/click?id=fallback_${Date.now()}`,
              conversionUrl: `/api/tracking/conversion?id=fallback_${Date.now()}`,
            },
            metadata: {
              bidAmount: 0,
              pricingModel: "FALLBACK",
              timestamp: new Date().toISOString(),
              frequencyBlocked: true
            }
          })
        } else {
          return NextResponse.json({
            success: false,
            error: "No ads available (frequency cap exceeded)",
            frequencyBlocked: true,
            timeUntilReset: frequencyCheck.timeUntilReset
          })
        }
      }
    }

    // 6. Log the ad request for analytics
    await logAdRequest(bidRequest, bidResponse)

    // 7. Return response
    if (bidResponse.success && bidResponse.ad) {
      return NextResponse.json({
        success: true,
        ad: {
          id: bidResponse.ad.id,
          title: bidResponse.ad.title,
          description: bidResponse.ad.description,
          imageUrl: bidResponse.ad.imageUrl,
          videoUrl: bidResponse.ad.videoUrl,
          clickUrl: bidResponse.ad.clickUrl,
          width: bidResponse.ad.width,
          height: bidResponse.ad.height,
          format: bidResponse.ad.format,
        },
        tracking: {
          impressionUrl: bidResponse.trackingUrls.impression,
          clickUrl: bidResponse.trackingUrls.click,
          conversionUrl: bidResponse.trackingUrls.conversion,
        },
        metadata: {
          bidAmount: bidResponse.bidAmount,
          pricingModel: bidResponse.pricingModel,
          timestamp: new Date().toISOString(),
        }
      })
    } else {
      // Return fallback ad or empty response
      return NextResponse.json({
        success: false,
        error: bidResponse.error || "No ads available",
        fallback: await getFallbackAd(bidRequest)
      })
    }
  } catch (error) {
    console.error("Ad request error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request parameters", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  // Support GET requests for simple ad requests (for testing)
  const { searchParams } = new URL(request.url)
  
  const adSpaceId = searchParams.get("adSpaceId")
  const apiKey = searchParams.get("apiKey")
  const format = searchParams.get("format")
  const width = searchParams.get("width")
  const height = searchParams.get("height")

  if (!adSpaceId || !apiKey || !format || !width || !height) {
    return NextResponse.json(
      { error: "Missing required parameters" },
      { status: 400 }
    )
  }

  // Convert to POST request format
  const body = {
    adSpaceId,
    apiKey,
    format,
    width: parseInt(width),
    height: parseInt(height),
  }

  // Create a new request with the body
  const postRequest = new NextRequest(request.url, {
    method: "POST",
    headers: request.headers,
    body: JSON.stringify(body),
  })

  return POST(postRequest)
}

async function logAdRequest(bidRequest: BidRequest, bidResponse: any): Promise<void> {
  try {
    // Log the ad request for analytics and debugging
    // This would typically go to a separate analytics database or service
    console.log("Ad Request:", {
      adSpaceId: bidRequest.adSpaceId,
      publisherId: bidRequest.publisherId,
      success: bidResponse.success,
      bidAmount: bidResponse.bidAmount,
      timestamp: bidRequest.timestamp,
    })

    // In a real implementation, you might:
    // 1. Store in a time-series database for analytics
    // 2. Send to a message queue for processing
    // 3. Update real-time metrics
    // 4. Trigger fraud detection algorithms
  } catch (error) {
    console.error("Failed to log ad request:", error)
    // Don't fail the main request if logging fails
  }
}

/**
 * Detect device type from user agent
 */
function getDeviceType(userAgent: string): string {
  const isMobile = /Mobile|Android|iPhone/i.test(userAgent)
  const isTablet = /iPad|Tablet/i.test(userAgent)

  if (isTablet) return "tablet"
  if (isMobile) return "mobile"
  return "desktop"
}


