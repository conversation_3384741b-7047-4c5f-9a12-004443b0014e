import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { Ad, AdFormat } from '@/lib/types'

interface AdsState {
  ads: Ad[]
  isLoading: boolean
  isCreating: boolean
  isUpdating: boolean
  error: string | null
  lastFetch: number | null
}

const initialState: AdsState = {
  ads: [],
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  error: null,
  lastFetch: null,
}

// Async thunks
export const fetchAds = createAsyncThunk(
  'ads/fetchAds',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/advertiser/ads')
      if (!response.ok) {
        throw new Error('Failed to fetch ads')
      }
      const data = await response.json()
      return data.ads || []
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

export const createAd = createAsyncThunk(
  'ads/createAd',
  async (adData: Omit<Ad, 'id' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      // Prepare the ad data, removing empty URLs
      const cleanedAdData = {
        ...adData,
        imageUrl: adData.imageUrl || undefined,
        videoUrl: adData.videoUrl || undefined,
      }

      const response = await fetch('/api/advertiser/ads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cleanedAdData),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to create ad')
      }
      
      const data = await response.json()
      return data.ad
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

export const updateAd = createAsyncThunk(
  'ads/updateAd',
  async ({ id, updates }: { id: string; updates: Partial<Ad> }, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/advertiser/ads/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to update ad')
      }
      
      const data = await response.json()
      return { id, updates: data.ad }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

export const deleteAd = createAsyncThunk(
  'ads/deleteAd',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/advertiser/ads/${id}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to delete ad')
      }
      
      return id
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

export const pauseAd = createAsyncThunk(
  'ads/pauseAd',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/advertiser/ads/${id}/pause`, {
        method: 'POST',
      })
      
      if (!response.ok) {
        throw new Error('Failed to pause ad')
      }
      
      return id
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

export const resumeAd = createAsyncThunk(
  'ads/resumeAd',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/advertiser/ads/${id}/resume`, {
        method: 'POST',
      })
      
      if (!response.ok) {
        throw new Error('Failed to resume ad')
      }
      
      return id
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

const adsSlice = createSlice({
  name: 'ads',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    updateAdMetrics: (state, action: PayloadAction<{ id: string; metrics: Partial<Ad> }>) => {
      const ad = state.ads.find(a => a.id === action.payload.id)
      if (ad) {
        Object.assign(ad, action.payload.metrics)
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch ads
      .addCase(fetchAds.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchAds.fulfilled, (state, action) => {
        state.isLoading = false
        state.ads = action.payload
        state.lastFetch = Date.now()
      })
      .addCase(fetchAds.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
      // Create ad
      .addCase(createAd.pending, (state) => {
        state.isCreating = true
        state.error = null
      })
      .addCase(createAd.fulfilled, (state, action) => {
        state.isCreating = false
        state.ads.push(action.payload)
      })
      .addCase(createAd.rejected, (state, action) => {
        state.isCreating = false
        state.error = action.payload as string
      })
      // Update ad
      .addCase(updateAd.pending, (state) => {
        state.isUpdating = true
        state.error = null
      })
      .addCase(updateAd.fulfilled, (state, action) => {
        state.isUpdating = false
        const index = state.ads.findIndex(a => a.id === action.payload.id)
        if (index !== -1) {
          state.ads[index] = { ...state.ads[index], ...action.payload.updates }
        }
      })
      .addCase(updateAd.rejected, (state, action) => {
        state.isUpdating = false
        state.error = action.payload as string
      })
      // Delete ad
      .addCase(deleteAd.fulfilled, (state, action) => {
        state.ads = state.ads.filter(a => a.id !== action.payload)
      })
      // Pause ad
      .addCase(pauseAd.fulfilled, (state, action) => {
        const ad = state.ads.find(a => a.id === action.payload)
        if (ad) {
          ad.isActive = false
        }
      })
      // Resume ad
      .addCase(resumeAd.fulfilled, (state, action) => {
        const ad = state.ads.find(a => a.id === action.payload)
        if (ad) {
          ad.isActive = true
        }
      })
  },
})

export const { clearError, updateAdMetrics } = adsSlice.actions
export default adsSlice.reducer
