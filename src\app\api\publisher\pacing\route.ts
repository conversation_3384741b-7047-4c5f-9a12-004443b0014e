import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { PublisherPacingService } from "@/lib/pacing/publisher-pacing"

// GET /api/publisher/pacing - Get pacing settings and insights
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "PUBLISHER") {
      return NextResponse.json(
        { message: "Only publishers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get publisher profile
    const publisherProfile = await prisma.publisherProfile.findUnique({
      where: { userId: session.user.id }
    })

    if (!publisherProfile) {
      return NextResponse.json(
        { message: "Publisher profile not found" },
        { status: 404 }
      )
    }

    // Initialize pacing if not exists
    const pacingService = PublisherPacingService.getInstance()
    await pacingService.initializePacing(publisherProfile.id)

    // Get pacing insights
    const insights = await pacingService.getPacingInsights(publisherProfile.id)

    return NextResponse.json({
      success: true,
      data: insights
    })

  } catch (error) {
    console.error("Error fetching publisher pacing:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/publisher/pacing - Update pacing settings
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "PUBLISHER") {
      return NextResponse.json(
        { message: "Only publishers can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      dailyRevenuePacing,
      weeklyRevenuePacing,
      monthlyRevenuePacing,
      adServingStrategy,
      minFillRate,
      maxAdFrequency,
      dailyRevenueTarget,
      weeklyRevenueTarget,
      monthlyRevenueTarget,
      minAdQualityScore,
      blockLowPerformingAds
    } = body

    // Get publisher profile
    const publisherProfile = await prisma.publisherProfile.findUnique({
      where: { userId: session.user.id }
    })

    if (!publisherProfile) {
      return NextResponse.json(
        { message: "Publisher profile not found" },
        { status: 404 }
      )
    }

    // Validate input values
    if (minFillRate && (minFillRate < 0 || minFillRate > 1)) {
      return NextResponse.json(
        { message: "Min fill rate must be between 0 and 1 (100%)" },
        { status: 400 }
      )
    }

    if (maxAdFrequency && (maxAdFrequency < 1 || maxAdFrequency > 10)) {
      return NextResponse.json(
        { message: "Max ad frequency must be between 1 and 10" },
        { status: 400 }
      )
    }

    if (minAdQualityScore && (minAdQualityScore < 0 || minAdQualityScore > 1)) {
      return NextResponse.json(
        { message: "Min ad quality score must be between 0 and 1" },
        { status: 400 }
      )
    }

    // Update pacing settings
    const updatedPacing = await prisma.publisherPacing.upsert({
      where: { publisherId: publisherProfile.id },
      update: {
        dailyRevenuePacing: dailyRevenuePacing || undefined,
        weeklyRevenuePacing: weeklyRevenuePacing || undefined,
        monthlyRevenuePacing: monthlyRevenuePacing || undefined,
        adServingStrategy: adServingStrategy || undefined,
        minFillRate: minFillRate !== undefined ? minFillRate : undefined,
        maxAdFrequency: maxAdFrequency !== undefined ? maxAdFrequency : undefined,
        dailyRevenueTarget: dailyRevenueTarget !== undefined ? dailyRevenueTarget : undefined,
        weeklyRevenueTarget: weeklyRevenueTarget !== undefined ? weeklyRevenueTarget : undefined,
        monthlyRevenueTarget: monthlyRevenueTarget !== undefined ? monthlyRevenueTarget : undefined,
        minAdQualityScore: minAdQualityScore !== undefined ? minAdQualityScore : undefined,
        blockLowPerformingAds: blockLowPerformingAds !== undefined ? blockLowPerformingAds : undefined,
        updatedAt: new Date()
      },
      create: {
        publisherId: publisherProfile.id,
        dailyRevenuePacing: dailyRevenuePacing || "EVEN",
        weeklyRevenuePacing: weeklyRevenuePacing || "EVEN",
        monthlyRevenuePacing: monthlyRevenuePacing || "EVEN",
        adServingStrategy: adServingStrategy || "REVENUE_OPTIMIZED",
        minFillRate: minFillRate || 0.8,
        maxAdFrequency: maxAdFrequency || 3,
        dailyRevenueTarget,
        weeklyRevenueTarget,
        monthlyRevenueTarget,
        minAdQualityScore: minAdQualityScore || 0.7,
        blockLowPerformingAds: blockLowPerformingAds !== undefined ? blockLowPerformingAds : true
      }
    })

    return NextResponse.json({
      success: true,
      message: "Pacing settings updated successfully",
      data: updatedPacing
    })

  } catch (error) {
    console.error("Error updating publisher pacing:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/publisher/pacing/optimize - Trigger manual optimization
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "PUBLISHER") {
      return NextResponse.json(
        { message: "Only publishers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get publisher profile
    const publisherProfile = await prisma.publisherProfile.findUnique({
      where: { userId: session.user.id }
    })

    if (!publisherProfile) {
      return NextResponse.json(
        { message: "Publisher profile not found" },
        { status: 404 }
      )
    }

    // Run optimization for this specific publisher
    const pacingService = PublisherPacingService.getInstance()
    await pacingService.runPacingOptimization()

    // Get updated insights
    const insights = await pacingService.getPacingInsights(publisherProfile.id)

    return NextResponse.json({
      success: true,
      message: "Pacing optimization completed",
      data: insights
    })

  } catch (error) {
    console.error("Error running publisher pacing optimization:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
