import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { FrequencyManager } from "@/lib/frequency/frequency-manager"

// GET /api/advertiser/frequency - Get frequency analytics for advertiser campaigns
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get advertiser profile
    const advertiserProfile = await prisma.advertiserProfile.findUnique({
      where: { userId: session.user.id }
    })

    if (!advertiserProfile) {
      return NextResponse.json(
        { message: "Advertiser profile not found" },
        { status: 404 }
      )
    }

    // Get advertiser's campaigns
    const campaigns = await prisma.campaign.findMany({
      where: { 
        advertiserId: advertiserProfile.id,
        status: { in: ["ACTIVE", "PAUSED"] }
      },
      select: {
        id: true,
        name: true,
        frequencyCap: true,
        frequencyPeriod: true,
        status: true
      }
    })

    // Get frequency analytics for each campaign
    const frequencyManager = FrequencyManager.getInstance()
    const campaignAnalytics = []

    for (const campaign of campaigns) {
      const analytics = await frequencyManager.getCampaignFrequencyAnalytics(campaign.id)
      campaignAnalytics.push({
        campaign,
        analytics
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        campaigns: campaignAnalytics,
        summary: {
          totalCampaigns: campaigns.length,
          activeCampaigns: campaigns.filter(c => c.status === "ACTIVE").length,
          totalUsers: campaignAnalytics.reduce((sum, c) => sum + c.analytics.totalUsers, 0),
          averageFrequency: campaignAnalytics.reduce((sum, c) => sum + c.analytics.averageFrequency, 0) / Math.max(campaignAnalytics.length, 1)
        }
      }
    })

  } catch (error) {
    console.error("Error fetching frequency analytics:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/advertiser/frequency - Update campaign frequency settings
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { campaignId, frequencyCap, frequencyPeriod } = body

    // Validate input
    if (!campaignId || !frequencyCap || !frequencyPeriod) {
      return NextResponse.json(
        { message: "Campaign ID, frequency cap, and frequency period are required" },
        { status: 400 }
      )
    }

    if (frequencyCap < 1 || frequencyCap > 50) {
      return NextResponse.json(
        { message: "Frequency cap must be between 1 and 50" },
        { status: 400 }
      )
    }

    if (!["DAILY", "WEEKLY", "CAMPAIGN"].includes(frequencyPeriod)) {
      return NextResponse.json(
        { message: "Frequency period must be DAILY, WEEKLY, or CAMPAIGN" },
        { status: 400 }
      )
    }

    // Get advertiser profile
    const advertiserProfile = await prisma.advertiserProfile.findUnique({
      where: { userId: session.user.id }
    })

    if (!advertiserProfile) {
      return NextResponse.json(
        { message: "Advertiser profile not found" },
        { status: 404 }
      )
    }

    // Verify campaign ownership
    const campaign = await prisma.campaign.findFirst({
      where: {
        id: campaignId,
        advertiserId: advertiserProfile.id
      }
    })

    if (!campaign) {
      return NextResponse.json(
        { message: "Campaign not found or access denied" },
        { status: 404 }
      )
    }

    // Update campaign frequency settings
    const updatedCampaign = await prisma.campaign.update({
      where: { id: campaignId },
      data: {
        frequencyCap,
        frequencyPeriod
      }
    })

    return NextResponse.json({
      success: true,
      message: "Frequency settings updated successfully",
      data: {
        campaignId: updatedCampaign.id,
        frequencyCap: updatedCampaign.frequencyCap,
        frequencyPeriod: updatedCampaign.frequencyPeriod
      }
    })

  } catch (error) {
    console.error("Error updating frequency settings:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// GET /api/advertiser/frequency/[campaignId] - Get detailed frequency analytics for specific campaign
export async function GET_CAMPAIGN(request: NextRequest, { params }: { params: { campaignId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    const { campaignId } = params

    // Get advertiser profile
    const advertiserProfile = await prisma.advertiserProfile.findUnique({
      where: { userId: session.user.id }
    })

    if (!advertiserProfile) {
      return NextResponse.json(
        { message: "Advertiser profile not found" },
        { status: 404 }
      )
    }

    // Verify campaign ownership
    const campaign = await prisma.campaign.findFirst({
      where: {
        id: campaignId,
        advertiserId: advertiserProfile.id
      },
      include: {
        ads: {
          select: {
            id: true,
            title: true,
            imageUrl: true
          }
        }
      }
    })

    if (!campaign) {
      return NextResponse.json(
        { message: "Campaign not found or access denied" },
        { status: 404 }
      )
    }

    // Get detailed frequency analytics
    const frequencyManager = FrequencyManager.getInstance()
    const analytics = await frequencyManager.getCampaignFrequencyAnalytics(campaignId)

    // Get recent frequency tracking records
    const recentTracking = await prisma.frequencyTracking.findMany({
      where: { campaignId },
      orderBy: { lastSeen: "desc" },
      take: 50,
      select: {
        fingerprint: true,
        userId: true,
        impressions: true,
        lastSeen: true,
        deviceType: true,
        ipAddress: true
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        campaign: {
          id: campaign.id,
          name: campaign.name,
          frequencyCap: campaign.frequencyCap,
          frequencyPeriod: campaign.frequencyPeriod,
          status: campaign.status,
          ads: campaign.ads
        },
        analytics,
        recentTracking: recentTracking.map(record => ({
          userIdentifier: (record.userId || record.fingerprint).substring(0, 8) + "...",
          impressions: record.impressions,
          lastSeen: record.lastSeen,
          deviceType: record.deviceType,
          ipAddress: record.ipAddress.replace(/\d+$/, "***") // Mask last part of IP
        }))
      }
    })

  } catch (error) {
    console.error("Error fetching campaign frequency analytics:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
