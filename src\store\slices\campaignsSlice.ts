import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { Campaign, CampaignStatus, PricingModel } from '@/lib/types'

interface CampaignsState {
  campaigns: Campaign[]
  isLoading: boolean
  isCreating: boolean
  isUpdating: boolean
  error: string | null
  lastFetch: number | null
}

const initialState: CampaignsState = {
  campaigns: [],
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  error: null,
  lastFetch: null,
}

// Async thunks
export const fetchCampaigns = createAsyncThunk(
  'campaigns/fetchCampaigns',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/advertiser/campaigns')
      if (!response.ok) {
        throw new Error('Failed to fetch campaigns')
      }
      const data = await response.json()
      return data.campaigns || []
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

export const createCampaign = createAsyncThunk(
  'campaigns/createCampaign',
  async (campaignData: Omit<Campaign, 'id' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/advertiser/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(campaignData),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to create campaign')
      }
      
      const data = await response.json()
      return data.campaign
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

export const updateCampaign = createAsyncThunk(
  'campaigns/updateCampaign',
  async ({ id, updates }: { id: string; updates: Partial<Campaign> }, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/advertiser/campaigns/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to update campaign')
      }
      
      const data = await response.json()
      return { id, updates: data.campaign }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

export const deleteCampaign = createAsyncThunk(
  'campaigns/deleteCampaign',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/advertiser/campaigns/${id}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to delete campaign')
      }
      
      return id
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

export const pauseCampaign = createAsyncThunk(
  'campaigns/pauseCampaign',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/advertiser/campaigns/${id}/pause`, {
        method: 'POST',
      })
      
      if (!response.ok) {
        throw new Error('Failed to pause campaign')
      }
      
      return id
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

export const resumeCampaign = createAsyncThunk(
  'campaigns/resumeCampaign',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/advertiser/campaigns/${id}/resume`, {
        method: 'POST',
      })
      
      if (!response.ok) {
        throw new Error('Failed to resume campaign')
      }
      
      return id
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error')
    }
  }
)

const campaignsSlice = createSlice({
  name: 'campaigns',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    updateCampaignStatus: (state, action: PayloadAction<{ id: string; status: CampaignStatus }>) => {
      const campaign = state.campaigns.find(c => c.id === action.payload.id)
      if (campaign) {
        campaign.status = action.payload.status
      }
    },
    updateCampaignBudget: (state, action: PayloadAction<{ id: string; spent: number }>) => {
      const campaign = state.campaigns.find(c => c.id === action.payload.id)
      if (campaign) {
        campaign.spent = action.payload.spent
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch campaigns
      .addCase(fetchCampaigns.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchCampaigns.fulfilled, (state, action) => {
        state.isLoading = false
        state.campaigns = action.payload
        state.lastFetch = Date.now()
      })
      .addCase(fetchCampaigns.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
      // Create campaign
      .addCase(createCampaign.pending, (state) => {
        state.isCreating = true
        state.error = null
      })
      .addCase(createCampaign.fulfilled, (state, action) => {
        state.isCreating = false
        state.campaigns.push(action.payload)
      })
      .addCase(createCampaign.rejected, (state, action) => {
        state.isCreating = false
        state.error = action.payload as string
      })
      // Update campaign
      .addCase(updateCampaign.pending, (state) => {
        state.isUpdating = true
        state.error = null
      })
      .addCase(updateCampaign.fulfilled, (state, action) => {
        state.isUpdating = false
        const index = state.campaigns.findIndex(c => c.id === action.payload.id)
        if (index !== -1) {
          state.campaigns[index] = { ...state.campaigns[index], ...action.payload.updates }
        }
      })
      .addCase(updateCampaign.rejected, (state, action) => {
        state.isUpdating = false
        state.error = action.payload as string
      })
      // Delete campaign
      .addCase(deleteCampaign.fulfilled, (state, action) => {
        state.campaigns = state.campaigns.filter(c => c.id !== action.payload)
      })
      // Pause campaign
      .addCase(pauseCampaign.fulfilled, (state, action) => {
        const campaign = state.campaigns.find(c => c.id === action.payload)
        if (campaign) {
          campaign.status = 'PAUSED'
        }
      })
      // Resume campaign
      .addCase(resumeCampaign.fulfilled, (state, action) => {
        const campaign = state.campaigns.find(c => c.id === action.payload)
        if (campaign) {
          campaign.status = 'ACTIVE'
        }
      })
  },
})

export const { clearError, updateCampaignStatus, updateCampaignBudget } = campaignsSlice.actions
export default campaignsSlice.reducer
