"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { toast } from "sonner"
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  DollarSign, 
  BarChart3, 
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock
} from "lucide-react"

interface PacingInsights {
  pacingSettings: any
  campaignInsights: any[]
  overallPerformance: any
}

export default function PacingDashboard() {
  const [insights, setInsights] = useState<PacingInsights | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [settings, setSettings] = useState({
    dailyBudgetPacing: "EVEN",
    bidPacingStrategy: "PERFORMANCE_BASED",
    targetCTR: 2.0,
    targetConversionRate: 5.0,
    maxBidIncrease: 0.5,
    maxBidDecrease: 0.3,
    targetCPA: "",
    targetROAS: ""
  })

  useEffect(() => {
    fetchPacingInsights()
  }, [])

  const fetchPacingInsights = async () => {
    try {
      const response = await fetch("/api/advertiser/pacing")
      if (response.ok) {
        const data = await response.json()
        setInsights(data.data)
        if (data.data?.pacingSettings) {
          setSettings({
            dailyBudgetPacing: data.data.pacingSettings.dailyBudgetPacing || "EVEN",
            bidPacingStrategy: data.data.pacingSettings.bidPacingStrategy || "PERFORMANCE_BASED",
            targetCTR: data.data.pacingSettings.targetCTR || 2.0,
            targetConversionRate: data.data.pacingSettings.targetConversionRate || 5.0,
            maxBidIncrease: data.data.pacingSettings.maxBidIncrease || 0.5,
            maxBidDecrease: data.data.pacingSettings.maxBidDecrease || 0.3,
            targetCPA: data.data.pacingSettings.targetCPA?.toString() || "",
            targetROAS: data.data.pacingSettings.targetROAS?.toString() || ""
          })
        }
      } else {
        toast.error("Failed to fetch pacing insights")
      }
    } catch (error) {
      console.error("Error fetching pacing insights:", error)
      toast.error("Error loading pacing data")
    } finally {
      setLoading(false)
    }
  }

  const updatePacingSettings = async () => {
    setUpdating(true)
    try {
      const payload = {
        ...settings,
        targetCPA: settings.targetCPA ? parseFloat(settings.targetCPA) : null,
        targetROAS: settings.targetROAS ? parseFloat(settings.targetROAS) : null
      }

      const response = await fetch("/api/advertiser/pacing", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload)
      })

      if (response.ok) {
        toast.success("Pacing settings updated successfully!")
        fetchPacingInsights()
      } else {
        const error = await response.json()
        toast.error(error.message || "Failed to update settings")
      }
    } catch (error) {
      console.error("Error updating pacing settings:", error)
      toast.error("Error updating settings")
    } finally {
      setUpdating(false)
    }
  }

  const triggerOptimization = async () => {
    try {
      const response = await fetch("/api/advertiser/pacing/optimize", {
        method: "POST"
      })

      if (response.ok) {
        toast.success("Optimization triggered successfully!")
        fetchPacingInsights()
      } else {
        toast.error("Failed to trigger optimization")
      }
    } catch (error) {
      console.error("Error triggering optimization:", error)
      toast.error("Error triggering optimization")
    }
  }

  const getPacingStatusColor = (status: string) => {
    switch (status) {
      case "ON_TRACK": return "bg-green-500"
      case "OVERSPENDING": return "bg-red-500"
      case "UNDERSPENDING": return "bg-yellow-500"
      default: return "bg-gray-500"
    }
  }

  const getPacingStatusIcon = (status: string) => {
    switch (status) {
      case "ON_TRACK": return <CheckCircle className="h-4 w-4" />
      case "OVERSPENDING": return <AlertTriangle className="h-4 w-4" />
      case "UNDERSPENDING": return <Clock className="h-4 w-4" />
      default: return <BarChart3 className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Pacing Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor and optimize your campaign performance with intelligent pacing
          </p>
        </div>
        <Button onClick={triggerOptimization} variant="outline">
          <TrendingUp className="h-4 w-4 mr-2" />
          Optimize Now
        </Button>
      </div>

      {/* Overall Performance Cards */}
      {insights?.overallPerformance && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Impressions</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {insights.overallPerformance.impressions.toLocaleString()}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average CTR</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {insights.overallPerformance.averageCTR.toFixed(2)}%
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spend</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ₹{insights.overallPerformance.cost.toFixed(2)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg CPA</CardTitle>
              <TrendingDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ₹{insights.overallPerformance.averageCPA.toFixed(2)}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="campaigns" className="space-y-4">
        <TabsList>
          <TabsTrigger value="campaigns">Campaign Performance</TabsTrigger>
          <TabsTrigger value="settings">Pacing Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="campaigns" className="space-y-4">
          {insights?.campaignInsights?.map((campaign) => (
            <Card key={campaign.campaignId}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">{campaign.campaignName}</CardTitle>
                    <CardDescription>
                      Campaign ID: {campaign.campaignId}
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge 
                      className={`${getPacingStatusColor(campaign.pacingStatus)} text-white`}
                    >
                      {getPacingStatusIcon(campaign.pacingStatus)}
                      <span className="ml-1">{campaign.pacingStatus.replace('_', ' ')}</span>
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <Label className="text-sm text-muted-foreground">Budget Progress</Label>
                    <Progress 
                      value={campaign.budgetUtilization * 100} 
                      className="mt-1"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      {(campaign.budgetUtilization * 100).toFixed(1)}% of budget used
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm text-muted-foreground">Time Progress</Label>
                    <Progress 
                      value={campaign.timeProgress * 100} 
                      className="mt-1"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      {(campaign.timeProgress * 100).toFixed(1)}% of time elapsed
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm text-muted-foreground">Performance</Label>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-sm">CTR: {campaign.metrics.ctr.toFixed(2)}%</span>
                      <span className="text-sm">Conv: {campaign.metrics.conversionRate.toFixed(2)}%</span>
                    </div>
                  </div>
                </div>

                {campaign.recommendations.confidence > 0.7 && (
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <h4 className="font-medium text-sm mb-1">Recommendation</h4>
                    <p className="text-sm text-muted-foreground">
                      {campaign.recommendations.reason}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Confidence: {(campaign.recommendations.confidence * 100).toFixed(0)}%
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                Pacing Configuration
              </CardTitle>
              <CardDescription>
                Configure how your campaigns should pace their spending and optimize bids
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="dailyBudgetPacing">Daily Budget Pacing</Label>
                    <Select 
                      value={settings.dailyBudgetPacing} 
                      onValueChange={(value) => setSettings({...settings, dailyBudgetPacing: value})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="EVEN">Even Distribution</SelectItem>
                        <SelectItem value="FRONT_LOADED">Front Loaded</SelectItem>
                        <SelectItem value="BACK_LOADED">Back Loaded</SelectItem>
                        <SelectItem value="AGGRESSIVE">Aggressive</SelectItem>
                        <SelectItem value="CONSERVATIVE">Conservative</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="bidPacingStrategy">Bid Pacing Strategy</Label>
                    <Select 
                      value={settings.bidPacingStrategy} 
                      onValueChange={(value) => setSettings({...settings, bidPacingStrategy: value})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="PERFORMANCE_BASED">Performance Based</SelectItem>
                        <SelectItem value="BUDGET_BASED">Budget Based</SelectItem>
                        <SelectItem value="COMPETITION_BASED">Competition Based</SelectItem>
                        <SelectItem value="TIME_BASED">Time Based</SelectItem>
                        <SelectItem value="FIXED">Fixed Bids</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="targetCTR">Target CTR (%)</Label>
                    <Input
                      id="targetCTR"
                      type="number"
                      step="0.1"
                      value={settings.targetCTR}
                      onChange={(e) => setSettings({...settings, targetCTR: parseFloat(e.target.value) || 0})}
                    />
                  </div>

                  <div>
                    <Label htmlFor="targetConversionRate">Target Conversion Rate (%)</Label>
                    <Input
                      id="targetConversionRate"
                      type="number"
                      step="0.1"
                      value={settings.targetConversionRate}
                      onChange={(e) => setSettings({...settings, targetConversionRate: parseFloat(e.target.value) || 0})}
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="maxBidIncrease">Max Bid Increase (%)</Label>
                  <Input
                    id="maxBidIncrease"
                    type="number"
                    step="0.1"
                    max="2"
                    value={settings.maxBidIncrease * 100}
                    onChange={(e) => setSettings({...settings, maxBidIncrease: (parseFloat(e.target.value) || 0) / 100})}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Maximum percentage to increase bids (0-200%)
                  </p>
                </div>

                <div>
                  <Label htmlFor="maxBidDecrease">Max Bid Decrease (%)</Label>
                  <Input
                    id="maxBidDecrease"
                    type="number"
                    step="0.1"
                    max="1"
                    value={settings.maxBidDecrease * 100}
                    onChange={(e) => setSettings({...settings, maxBidDecrease: (parseFloat(e.target.value) || 0) / 100})}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Maximum percentage to decrease bids (0-100%)
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="targetCPA">Target CPA (₹) - Optional</Label>
                  <Input
                    id="targetCPA"
                    type="number"
                    step="0.01"
                    placeholder="e.g., 50.00"
                    value={settings.targetCPA}
                    onChange={(e) => setSettings({...settings, targetCPA: e.target.value})}
                  />
                </div>

                <div>
                  <Label htmlFor="targetROAS">Target ROAS - Optional</Label>
                  <Input
                    id="targetROAS"
                    type="number"
                    step="0.1"
                    placeholder="e.g., 3.0"
                    value={settings.targetROAS}
                    onChange={(e) => setSettings({...settings, targetROAS: e.target.value})}
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={fetchPacingInsights}>
                  Reset
                </Button>
                <Button onClick={updatePacingSettings} disabled={updating}>
                  {updating ? "Updating..." : "Save Settings"}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
