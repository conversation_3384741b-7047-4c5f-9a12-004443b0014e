import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { generateApiKey, generateApiSecret, encryptApiSecret, decryptApiSecret } from "@/lib/crypto"

// GET /api/publisher/api-keys - Get publisher API credentials
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "PUBLISHER") {
      return NextResponse.json(
        { message: "Only publishers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get publisher profile
    const publisherProfile = await prisma.publisherProfile.findUnique({
      where: { userId: session.user.id },
    })

    if (!publisherProfile) {
      return NextResponse.json(
        { message: "Publisher profile not found" },
        { status: 404 }
      )
    }

    // Decrypt API secret for display
    let decryptedSecret = null
    if (publisherProfile.apiSecret) {
      try {
        decryptedSecret = decryptApiSecret(publisherProfile.apiSecret)
      } catch (error) {
        console.error("Failed to decrypt API secret:", error)
      }
    }

    return NextResponse.json({
      credentials: {
        apiKey: publisherProfile.apiKey,
        apiSecret: decryptedSecret,
        createdAt: publisherProfile.createdAt,
      },
      usage: {
        // TODO: Implement actual usage tracking
        requestsToday: 0,
        requestsThisMonth: 0,
        lastUsed: null,
      },
    })
  } catch (error) {
    console.error("API keys fetch error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/publisher/api-keys - Regenerate API credentials
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "PUBLISHER") {
      return NextResponse.json(
        { message: "Only publishers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get publisher profile
    const publisherProfile = await prisma.publisherProfile.findUnique({
      where: { userId: session.user.id },
    })

    if (!publisherProfile) {
      return NextResponse.json(
        { message: "Publisher profile not found" },
        { status: 404 }
      )
    }

    // Generate new API credentials
    const newApiKey = generateApiKey()
    const newApiSecret = generateApiSecret()
    const encryptedSecret = encryptApiSecret(newApiSecret)

    // Update publisher profile with new credentials
    const updatedProfile = await prisma.publisherProfile.update({
      where: { id: publisherProfile.id },
      data: {
        apiKey: newApiKey,
        apiSecret: encryptedSecret,
      },
    })

    return NextResponse.json({
      message: "API credentials regenerated successfully",
      credentials: {
        apiKey: newApiKey,
        apiSecret: newApiSecret,
        createdAt: updatedProfile.updatedAt,
      },
      warning: "Please update your integration code with the new API key. The old key will stop working immediately.",
    })
  } catch (error) {
    console.error("API keys regeneration error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
