import Razorpay from "razorpay"

if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_KEY_SECRET) {
  throw new Error("Razorpay credentials are not configured")
}

export const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID,
  key_secret: process.env.RAZORPAY_KEY_SECRET,
})

// Razorpay types (since @types/razorpay doesn't exist)
export interface RazorpayOrder {
  id: string
  entity: string
  amount: number
  amount_paid: number
  amount_due: number
  currency: string
  receipt: string
  offer_id?: string
  status: "created" | "attempted" | "paid"
  attempts: number
  notes: Record<string, string>
  created_at: number
}

export interface RazorpayPayment {
  id: string
  entity: string
  amount: number
  currency: string
  status: "created" | "authorized" | "captured" | "refunded" | "failed"
  order_id: string
  invoice_id?: string
  international: boolean
  method: string
  amount_refunded: number
  refund_status?: string
  captured: boolean
  description?: string
  card_id?: string
  bank?: string
  wallet?: string
  vpa?: string
  email: string
  contact: string
  notes: Record<string, string>
  fee?: number
  tax?: number
  error_code?: string
  error_description?: string
  error_source?: string
  error_step?: string
  error_reason?: string
  acquirer_data?: Record<string, any>
  created_at: number
}

export interface RazorpayWebhookEvent {
  entity: string
  account_id: string
  event: string
  contains: string[]
  payload: {
    payment: {
      entity: RazorpayPayment
    }
    order: {
      entity: RazorpayOrder
    }
  }
  created_at: number
}

// Utility functions
export const formatAmountForRazorpay = (amount: number): number => {
  // Razorpay expects amount in paise (smallest currency unit)
  return Math.round(amount * 100)
}

export const formatAmountFromRazorpay = (amount: number): number => {
  // Convert from paise to rupees
  return amount / 100
}

export const generateReceiptId = (advertiserId: string): string => {
  const timestamp = Date.now()
  return `ADV_${advertiserId.slice(-8)}_${timestamp}`
}

export const verifyPaymentSignature = (
  razorpayOrderId: string,
  razorpayPaymentId: string,
  razorpaySignature: string
): boolean => {
  const crypto = require("crypto")
  const body = razorpayOrderId + "|" + razorpayPaymentId
  const expectedSignature = crypto
    .createHmac("sha256", process.env.RAZORPAY_KEY_SECRET)
    .update(body.toString())
    .digest("hex")
  
  return expectedSignature === razorpaySignature
}
