import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { razorpay, formatAmountForRazorpay, generateReceiptId } from "@/lib/razorpay"
import { z } from "zod"

const createOrderSchema = z.object({
  amount: z.number().min(100, "Minimum amount is ₹100").max(100000, "Maximum amount is ₹1,00,000"),
  currency: z.string().default("INR"),
})

// POST /api/advertiser/payments/create-order - Create Razorpay order
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = createOrderSchema.parse(body)

    // Get advertiser profile
    const advertiserProfile = await prisma.advertiserProfile.findUnique({
      where: { userId: session.user.id },
      include: {
        user: true,
      },
    })

    if (!advertiserProfile) {
      return NextResponse.json(
        { message: "Advertiser profile not found" },
        { status: 404 }
      )
    }

    // Generate receipt ID
    const receiptId = generateReceiptId(advertiserProfile.id)

    // Create Razorpay order
    const razorpayOrder = await razorpay.orders.create({
      amount: formatAmountForRazorpay(validatedData.amount),
      currency: validatedData.currency,
      receipt: receiptId,
      notes: {
        advertiserId: advertiserProfile.id,
        userId: session.user.id,
        userEmail: advertiserProfile.user.email,
        purpose: "wallet_topup",
      },
    })

    // Save order to database
    const paymentOrder = await prisma.paymentOrder.create({
      data: {
        advertiserId: advertiserProfile.id,
        amount: validatedData.amount,
        currency: validatedData.currency,
        razorpayOrderId: razorpayOrder.id,
        status: "CREATED",
      },
    })

    // Create initial transaction record
    await prisma.paymentTransaction.create({
      data: {
        advertiserId: advertiserProfile.id,
        orderId: paymentOrder.id,
        amount: validatedData.amount,
        currency: validatedData.currency,
        status: "PENDING",
      },
    })

    return NextResponse.json({
      success: true,
      order: {
        id: paymentOrder.id,
        razorpayOrderId: razorpayOrder.id,
        amount: validatedData.amount,
        currency: validatedData.currency,
        key: process.env.RAZORPAY_KEY_ID,
      },
      razorpayOrder: {
        id: razorpayOrder.id,
        amount: razorpayOrder.amount,
        currency: razorpayOrder.currency,
      },
    })
  } catch (error) {
    console.error("Create payment order error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid request data", errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: "Failed to create payment order" },
      { status: 500 }
    )
  }
}
