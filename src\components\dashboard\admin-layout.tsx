"use client"

import { ReactNode } from "react"
import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { DashboardLayout } from "./dashboard-layout"
import { 
  BarChart3, 
  Users, 
  Settings, 
  Shield,
  Activity,
  Database
} from "lucide-react"

interface AdminLayoutProps {
  children: ReactNode
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const { data: session, status } = useSession()

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!session || session.user.role !== "ADMIN") {
    redirect("/auth/signin")
  }

  const navigation = [
    {
      name: "Overview",
      href: "/dashboard/admin",
      icon: BarChart3,
    },
    {
      name: "Bidding Analytics",
      href: "/dashboard/admin/bidding",
      icon: Activity,
    },
    {
      name: "User Management",
      href: "/dashboard/admin/users",
      icon: Users,
      disabled: true,
    },
    {
      name: "System Health",
      href: "/dashboard/admin/system",
      icon: Database,
      disabled: true,
    },
    {
      name: "Fraud Detection",
      href: "/dashboard/admin/fraud",
      icon: Shield,
      disabled: true,
    },
    {
      name: "Settings",
      href: "/dashboard/admin/settings",
      icon: Settings,
      disabled: true,
    },
  ]

  return (
    <DashboardLayout
      navigation={navigation}
      userRole="ADMIN"
      title="Admin Dashboard"
    >
      {children}
    </DashboardLayout>
  )
}
