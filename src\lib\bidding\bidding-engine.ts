import { PricingModel, AdFormat } from "@prisma/client"
import { adRequestLimiter, fraudDetectionLimiter, isSuspiciousIP, isBotUserAgent } from "@/lib/rate-limiter"

export interface BidRequest {
  adSpaceId: string
  publisherId: string
  format: AdFormat
  width: number
  height: number
  position: string
  publisherRegion: string
  publisherCategory: string
  userAgent?: string
  ipAddress?: string
  referrer?: string
  timestamp: Date
}

export interface BidResponse {
  success: boolean
  ad?: {
    id: string
    campaignId: string
    title: string
    description?: string
    imageUrl?: string
    videoUrl?: string
    clickUrl: string
    format: AdFormat
    width: number
    height: number
  }
  bidAmount: number
  pricingModel: PricingModel
  trackingUrls: {
    impression: string
    click: string
    conversion?: string
  }
  error?: string
}

export interface CampaignBid {
  campaignId: string
  adId: string
  bidAmount: number
  pricingModel: PricingModel
  score: number
  targetingMatch: number
  performanceScore: number
  budgetAvailable: number
}

export class BiddingEngine {
  private static instance: BiddingEngine
  private performanceCache: Map<string, number> = new Map()
  private fraudDetection: Set<string> = new Set()

  static getInstance(): BiddingEngine {
    if (!BiddingEngine.instance) {
      BiddingEngine.instance = new BiddingEngine()
    }
    return BiddingEngine.instance
  }

  async processBidRequest(request: BidRequest): Promise<BidResponse> {
    try {
      // 1. Validate request
      if (!this.validateBidRequest(request)) {
        return {
          success: false,
          bidAmount: 0,
          pricingModel: "CPC",
          trackingUrls: { impression: "", click: "" },
          error: "Invalid bid request"
        }
      }

      // 2. Fraud detection
      if (await this.detectFraud(request)) {
        return {
          success: false,
          bidAmount: 0,
          pricingModel: "CPC",
          trackingUrls: { impression: "", click: "" },
          error: "Request blocked by fraud detection"
        }
      }

      // 3. Find eligible campaigns
      const eligibleCampaigns = await this.findEligibleCampaigns(request)

      if (eligibleCampaigns.length === 0) {
        return {
          success: false,
          bidAmount: 0,
          pricingModel: "CPC",
          trackingUrls: { impression: "", click: "" },
          error: "No eligible campaigns found"
        }
      }

      // 4. Score and rank campaigns
      const rankedBids = await this.scoreAndRankCampaigns(eligibleCampaigns, request)

      // 5. Select winning bid
      const winningBid = rankedBids[0]
      const selectedAd = await this.getAdForCampaign(winningBid.campaignId, request)

      if (!selectedAd) {
        return {
          success: false,
          bidAmount: 0,
          pricingModel: "CPC",
          trackingUrls: { impression: "", click: "" },
          error: "No suitable ad found"
        }
      }

      // 6. Generate tracking URLs
      const trackingUrls = this.generateTrackingUrls(
        winningBid.campaignId,
        winningBid.adId,
        request.adSpaceId
      )

      // 7. Update campaign budget and metrics
      await this.updateCampaignMetrics(winningBid.campaignId, winningBid.bidAmount)

      return {
        success: true,
        ad: selectedAd,
        bidAmount: winningBid.bidAmount,
        pricingModel: winningBid.pricingModel,
        trackingUrls
      }
    } catch (error) {
      console.error("Bidding engine error:", error)
      return {
        success: false,
        bidAmount: 0,
        pricingModel: "CPC",
        trackingUrls: { impression: "", click: "" },
        error: "Internal bidding error"
      }
    }
  }

  private validateBidRequest(request: BidRequest): boolean {
    return !!(
      request.adSpaceId &&
      request.publisherId &&
      request.format &&
      request.width > 0 &&
      request.height > 0 &&
      request.publisherRegion &&
      request.publisherCategory
    )
  }

  private async detectFraud(request: BidRequest): Promise<boolean> {
    // Skip fraud detection in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log("Fraud detection bypassed in development mode")
      return false
    }

    // Check for suspicious IP patterns
    if (request.ipAddress && isSuspiciousIP(request.ipAddress)) {
      console.log("Blocked suspicious IP:", request.ipAddress)
      return true
    }

    // Check for bot user agents
    if (request.userAgent && isBotUserAgent(request.userAgent)) {
      console.log("Blocked bot user agent:", request.userAgent)
      return true
    }

    // Check for suspicious referrers
    if (request.referrer && this.isSuspiciousReferrer(request.referrer)) {
      console.log("Blocked suspicious referrer:", request.referrer)
      return true
    }

    // Rate limiting - check if IP is making too many requests
    if (request.ipAddress) {
      const rateLimit = await adRequestLimiter.checkLimit(request.ipAddress)
      if (!rateLimit.allowed) {
        console.log("Rate limit exceeded for IP:", request.ipAddress)
        return true
      }

      // Additional fraud detection for suspicious IPs
      if (this.fraudDetection.has(request.ipAddress)) {
        const fraudLimit = await fraudDetectionLimiter.checkLimit(request.ipAddress)
        if (!fraudLimit.allowed) {
          console.log("Fraud detection limit exceeded for IP:", request.ipAddress)
          return true
        }
      }
    }

    return false
  }

  private isSuspiciousReferrer(referrer: string): boolean {
    const suspiciousPatterns = [
      /localhost/i, /127\.0\.0\.1/i, /192\.168\./i,
      /test/i, /staging/i, /dev/i
    ]
    return suspiciousPatterns.some(pattern => pattern.test(referrer))
  }

  private async findEligibleCampaigns(request: BidRequest): Promise<any[]> {
    try {
      // Import prisma here to avoid circular dependencies
      const { prisma } = await import("@/lib/prisma")

      // Get publisher's ad space to understand targeting context
      const adSpace = await prisma.adSpace.findUnique({
        where: { id: request.adSpaceId },
        include: {
          publisher: {
            include: {
              user: true
            }
          }
        }
      })

      if (!adSpace) {
        console.error("Ad space not found:", request.adSpaceId)
        return []
      }

      // Calculate current date for daily budget tracking
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      // Query active campaigns with their ads and current spending
      const campaigns = await prisma.campaign.findMany({
        where: {
          status: "ACTIVE",
          startDate: {
            lte: new Date()
          },
          OR: [
            { endDate: null },
            { endDate: { gte: new Date() } }
          ],
          // Check if campaign has remaining budget
          budget: {
            gt: 0
          }
        },
        include: {
          ads: {
            where: {
              isActive: true,
              format: request.format,
              width: request.width,
              height: request.height
            }
          },
          adPlacements: {
            where: {
              createdAt: {
                gte: today
              }
            },
            select: {
              cost: true
            }
          },
          advertiser: {
            select: {
              balance: true,
              industry: true
            }
          }
        }
      })

      // Filter campaigns based on targeting and budget availability
      const eligibleCampaigns: any[] = []

      for (const campaign of campaigns) {
        // Check if campaign has suitable ads
        if (campaign.ads.length === 0) continue

        // Check advertiser balance
        if (campaign.advertiser.balance <= 0) continue

        // Calculate daily spending
        const dailySpent = campaign.adPlacements.reduce((sum: number, placement: any) => sum + placement.cost, 0)

        // Check daily budget
        if (campaign.dailyBudget && dailySpent >= campaign.dailyBudget) continue

        // Check total budget vs spending
        const totalSpent = await this.calculateTotalSpent(campaign.id)
        if (totalSpent >= campaign.budget) continue

        // Check targeting match
        const targetingMatch = this.calculateTargetingMatch(campaign, request)
        if (targetingMatch <= 0.3) continue // Minimum 30% targeting match

        // Add calculated spent for scoring
        eligibleCampaigns.push({
          ...campaign,
          spent: totalSpent
        })
      }

      return eligibleCampaigns
    } catch (error) {
      console.error("Error finding eligible campaigns:", error)
      return []
    }
  }

  private async calculateTotalSpent(campaignId: string): Promise<number> {
    try {
      const { prisma } = await import("@/lib/prisma")

      const result = await prisma.adPlacement.aggregate({
        where: { campaignId },
        _sum: { cost: true }
      })

      return result._sum.cost || 0
    } catch (error) {
      console.error("Error calculating total spent:", error)
      return 0
    }
  }

  private async scoreAndRankCampaigns(campaigns: any[], request: BidRequest): Promise<CampaignBid[]> {
    const bids: CampaignBid[] = []

    for (const campaign of campaigns) {
      // 1. Calculate targeting match score (0-1)
      const targetingMatch = this.calculateTargetingMatch(campaign, request)
      
      // 2. Get performance score from cache or calculate
      const performanceScore = this.getPerformanceScore(campaign.id)
      
      // 3. Check budget availability
      const budgetAvailable = campaign.budget - campaign.spent
      const dailyBudgetAvailable = campaign.dailyBudget - (campaign.spent * 0.1) // Mock daily spent
      
      if (budgetAvailable <= 0 || dailyBudgetAvailable <= 0) {
        continue // Skip campaigns without budget
      }

      // 4. Calculate final score
      const score = this.calculateFinalScore(
        campaign.bidAmount,
        targetingMatch,
        performanceScore,
        budgetAvailable
      )

      // 5. Find suitable ad
      const suitableAd = campaign.ads.find((ad: any) => 
        ad.format === request.format &&
        ad.width === request.width &&
        ad.height === request.height
      )

      if (suitableAd) {
        bids.push({
          campaignId: campaign.id,
          adId: suitableAd.id,
          bidAmount: campaign.bidAmount,
          pricingModel: campaign.pricingModel,
          score,
          targetingMatch,
          performanceScore,
          budgetAvailable
        })
      }
    }

    // Sort by score (highest first)
    return bids.sort((a, b) => b.score - a.score)
  }

  private calculateTargetingMatch(campaign: any, request: BidRequest): number {
    let score = 0
    let factors = 0

    // Region matching
    if (campaign.targetRegions.includes(request.publisherRegion) || 
        campaign.targetRegions.includes("Global")) {
      score += 1
    }
    factors++

    // Category matching
    if (campaign.targetCategories.includes(request.publisherCategory)) {
      score += 1
    }
    factors++

    return factors > 0 ? score / factors : 0
  }

  private getPerformanceScore(campaignId: string): number {
    // Get from cache or calculate based on historical performance
    const cached = this.performanceCache.get(campaignId)
    if (cached !== undefined) {
      return cached
    }

    // Mock performance score calculation
    const score = Math.random() * 0.3 + 0.7 // Score between 0.7-1.0
    this.performanceCache.set(campaignId, score)
    return score
  }

  private calculateFinalScore(
    bidAmount: number,
    targetingMatch: number,
    performanceScore: number,
    budgetAvailable: number
  ): number {
    // Weighted scoring algorithm
    const bidWeight = 0.4
    const targetingWeight = 0.3
    const performanceWeight = 0.2
    const budgetWeight = 0.1

    // Normalize bid amount (assuming max bid is $5)
    const normalizedBid = Math.min(bidAmount / 5, 1)
    
    // Normalize budget (assuming max budget is $10000)
    const normalizedBudget = Math.min(budgetAvailable / 10000, 1)

    return (
      normalizedBid * bidWeight +
      targetingMatch * targetingWeight +
      performanceScore * performanceWeight +
      normalizedBudget * budgetWeight
    )
  }

  private async getAdForCampaign(campaignId: string, request: BidRequest): Promise<any> {
    try {
      const { prisma } = await import("@/lib/prisma")

      const ad = await prisma.ad.findFirst({
        where: {
          campaignId,
          isActive: true,
          format: request.format,
          width: request.width,
          height: request.height
        }
      })

      return ad
    } catch (error) {
      console.error("Error getting ad for campaign:", error)
      return null
    }
  }

  private generateTrackingUrls(campaignId: string, adId: string, adSpaceId: string) {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
    const { generateTrackingId } = require("@/lib/tracking-utils")
    const trackingId = generateTrackingId(campaignId, adId, adSpaceId)

    return {
      impression: `${baseUrl}/api/tracking/impression?id=${trackingId}`,
      click: `${baseUrl}/api/tracking/click?id=${trackingId}`,
      conversion: `${baseUrl}/api/tracking/conversion?id=${trackingId}`
    }
  }

  private async updateCampaignMetrics(campaignId: string, bidAmount: number): Promise<void> {
    try {
      const { prisma } = await import("@/lib/prisma")

      // Log the winning bid for analytics
      console.log(`Campaign ${campaignId} won bid with amount ${bidAmount}`)

      // In a real implementation, you might:
      // 1. Update campaign performance metrics
      // 2. Adjust bid amounts based on performance
      // 3. Update budget allocation
      // 4. Send real-time notifications

    } catch (error) {
      console.error("Error updating campaign metrics:", error)
    }
  }
}
