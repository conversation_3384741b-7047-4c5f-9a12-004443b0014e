/**
 * Tracking utility functions for the Ad Network
 */

export interface TrackingData {
  campaignId: string
  adId: string
  adSpaceId: string
  timestamp: string
}

/**
 * Parse tracking ID to extract campaign, ad, and ad space information
 * Format: campaignId_adId_adSpaceId_timestamp
 */
export function parseTrackingId(trackingId: string): TrackingData | null {
  try {
    const parts = trackingId.split('_')
    
    if (parts.length < 4) {
      console.error('Invalid tracking ID format:', trackingId)
      return null
    }

    // The timestamp is always the last part
    const timestamp = parts[parts.length - 1]
    
    // The adSpaceId is the second to last part
    const adSpaceId = parts[parts.length - 2]
    
    // The adId is the third to last part
    const adId = parts[parts.length - 3]
    
    // Everything else is the campaignId (in case it contains underscores)
    const campaignId = parts.slice(0, parts.length - 3).join('_')

    return {
      campaignId,
      adId,
      adSpaceId,
      timestamp
    }
  } catch (error) {
    console.error('Error parsing tracking ID:', error)
    return null
  }
}

/**
 * Generate a tracking ID for impression/click/conversion tracking
 */
export function generateTrackingId(campaignId: string, adId: string, adSpaceId: string): string {
  const timestamp = Date.now().toString()
  return `${campaignId}_${adId}_${adSpaceId}_${timestamp}`
}

/**
 * Validate tracking ID format
 */
export function isValidTrackingId(trackingId: string): boolean {
  if (!trackingId || typeof trackingId !== 'string') {
    return false
  }

  const parts = trackingId.split('_')
  
  // Must have at least 4 parts: campaignId, adId, adSpaceId, timestamp
  if (parts.length < 4) {
    return false
  }

  // Last part should be a valid timestamp
  const timestamp = parts[parts.length - 1]
  const timestampNum = parseInt(timestamp, 10)
  
  if (isNaN(timestampNum) || timestampNum <= 0) {
    return false
  }

  // Check if timestamp is reasonable (not too old, not in future)
  const now = Date.now()
  const maxAge = 30 * 24 * 60 * 60 * 1000 // 30 days
  
  if (timestampNum < (now - maxAge) || timestampNum > (now + 60000)) { // Allow 1 minute in future for clock skew
    return false
  }

  return true
}

/**
 * Extract client IP address from request headers
 */
export function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const remoteAddr = request.headers.get('remote-addr')
  
  if (forwarded) {
    // x-forwarded-for can contain multiple IPs, take the first one
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP.trim()
  }
  
  if (remoteAddr) {
    return remoteAddr.trim()
  }
  
  return 'unknown'
}

/**
 * Sanitize user agent string
 */
export function sanitizeUserAgent(userAgent: string): string {
  if (!userAgent || typeof userAgent !== 'string') {
    return 'unknown'
  }
  
  // Limit length and remove potentially harmful characters
  return userAgent
    .slice(0, 500)
    .replace(/[<>'"]/g, '')
    .trim()
}

/**
 * Sanitize referrer URL
 */
export function sanitizeReferrer(referrer: string): string {
  if (!referrer || typeof referrer !== 'string') {
    return ''
  }
  
  try {
    const url = new URL(referrer)
    // Only keep the origin and pathname, remove query params and fragments for privacy
    return `${url.origin}${url.pathname}`
  } catch (error) {
    // If URL is invalid, return empty string
    return ''
  }
}

/**
 * Generate a fallback ad for when no campaigns are available
 */
export function getFallbackAd(request: any) {
  return {
    id: 'fallback',
    campaignId: 'fallback',
    title: 'Advertise Here',
    description: 'Reach your target audience with our ad network',
    imageUrl: `${process.env.NEXT_PUBLIC_APP_URL}/images/fallback-ad.svg`,
    clickUrl: `${process.env.NEXT_PUBLIC_APP_URL}/auth/signup?role=advertiser`,
    format: request.format || 'BANNER',
    width: request.width || 300,
    height: request.height || 250,
    tracking: {
      impression: `${process.env.NEXT_PUBLIC_APP_URL}/api/tracking/impression?id=fallback_fallback_${request.adSpaceId}_${Date.now()}`,
      click: `${process.env.NEXT_PUBLIC_APP_URL}/api/tracking/click?id=fallback_fallback_${request.adSpaceId}_${Date.now()}`,
      conversion: `${process.env.NEXT_PUBLIC_APP_URL}/api/tracking/conversion?id=fallback_fallback_${request.adSpaceId}_${Date.now()}`
    }
  }
}

/**
 * Calculate CTR (Click-Through Rate)
 */
export function calculateCTR(clicks: number, impressions: number): number {
  if (impressions === 0) return 0
  return (clicks / impressions) * 100
}

/**
 * Calculate CPC (Cost Per Click)
 */
export function calculateCPC(cost: number, clicks: number): number {
  if (clicks === 0) return 0
  return cost / clicks
}

/**
 * Calculate CPM (Cost Per Mille/Thousand Impressions)
 */
export function calculateCPM(cost: number, impressions: number): number {
  if (impressions === 0) return 0
  return (cost / impressions) * 1000
}

/**
 * Calculate CPA (Cost Per Acquisition)
 */
export function calculateCPA(cost: number, conversions: number): number {
  if (conversions === 0) return 0
  return cost / conversions
}

/**
 * Calculate ROAS (Return on Ad Spend)
 */
export function calculateROAS(revenue: number, cost: number): number {
  if (cost === 0) return 0
  return revenue / cost
}

/**
 * Format currency for display
 */
export function formatCurrency(amount: number, currency: string = 'INR'): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

/**
 * Format large numbers with appropriate suffixes
 */
export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

/**
 * Check if a timestamp is within a certain time window
 */
export function isWithinTimeWindow(timestamp: Date, windowMs: number): boolean {
  const now = new Date()
  const diff = now.getTime() - timestamp.getTime()
  return diff <= windowMs
}

/**
 * Generate a unique session ID for tracking
 */
export function generateSessionId(): string {
  return `sess_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}
