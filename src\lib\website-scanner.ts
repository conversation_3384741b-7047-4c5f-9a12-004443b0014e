import { AdFormat } from '@prisma/client'

// Dynamic import for JSDOM to avoid SSR issues
const getJSDOM = async () => {
  const { JSDOM } = await import('jsdom')
  return JSDOM
}

interface AdSpaceSuggestion {
  name: string
  format: AdFormat
  width: number
  height: number
  position: string
  selector: string
  confidence: number
  reasoning: string
}

interface ScanResult {
  success: boolean
  suggestions: AdSpaceSuggestion[]
  websiteInfo: {
    title: string
    description: string
    hasHeader: boolean
    hasSidebar: boolean
    hasFooter: boolean
    contentWidth: number
    mobileResponsive: boolean
  }
  error?: string
}

export class WebsiteScanner {
  private readonly USER_AGENT = 'AdNetwork-Scanner/1.0 (+https://yoursite.com/scanner)'
  private readonly TIMEOUT = 10000 // 10 seconds

  /**
   * Scan a website and suggest optimal ad placements
   */
  async scanWebsite(url: string): Promise<ScanResult> {
    try {
      // Validate URL
      const validatedUrl = this.validateUrl(url)
      if (!validatedUrl) {
        return {
          success: false,
          suggestions: [],
          websiteInfo: this.getDefaultWebsiteInfo(),
          error: 'Invalid URL provided'
        }
      }

      // Fetch website content
      const html = await this.fetchWebsiteContent(validatedUrl)
      if (!html) {
        return {
          success: false,
          suggestions: [],
          websiteInfo: this.getDefaultWebsiteInfo(),
          error: 'Failed to fetch website content'
        }
      }

      // Parse HTML and analyze structure
      const JSDOM = await getJSDOM()
      const dom = new JSDOM(html)
      const document = dom.window.document

      // Extract website information
      const websiteInfo = this.extractWebsiteInfo(document)

      // Generate ad space suggestions
      const suggestions = this.generateAdSpaceSuggestions(document, websiteInfo)

      return {
        success: true,
        suggestions,
        websiteInfo,
      }
    } catch (error) {
      console.error('Website scanning error:', error)
      return {
        success: false,
        suggestions: [],
        websiteInfo: this.getDefaultWebsiteInfo(),
        error: error instanceof Error ? error.message : 'Unknown scanning error'
      }
    }
  }

  /**
   * Validate and normalize URL
   */
  private validateUrl(url: string): string | null {
    try {
      // Add protocol if missing
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url
      }

      const urlObj = new URL(url)
      
      // Only allow http and https
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return null
      }

      return urlObj.toString()
    } catch {
      return null
    }
  }

  /**
   * Fetch website content with timeout and error handling
   */
  private async fetchWebsiteContent(url: string): Promise<string | null> {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.TIMEOUT)

      const response = await fetch(url, {
        headers: {
          'User-Agent': this.USER_AGENT,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Cache-Control': 'no-cache',
        },
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const contentType = response.headers.get('content-type')
      if (!contentType?.includes('text/html')) {
        throw new Error('Response is not HTML content')
      }

      return await response.text()
    } catch (error) {
      console.error('Failed to fetch website content:', error)
      return null
    }
  }

  /**
   * Extract basic website information
   */
  private extractWebsiteInfo(document: Document) {
    const title = document.querySelector('title')?.textContent?.trim() || 'Unknown'
    const description = document.querySelector('meta[name="description"]')?.getAttribute('content')?.trim() || ''
    
    // Check for common layout elements
    const hasHeader = !!(
      document.querySelector('header') ||
      document.querySelector('.header') ||
      document.querySelector('#header') ||
      document.querySelector('nav')
    )

    const hasSidebar = !!(
      document.querySelector('aside') ||
      document.querySelector('.sidebar') ||
      document.querySelector('#sidebar') ||
      document.querySelector('.side-bar')
    )

    const hasFooter = !!(
      document.querySelector('footer') ||
      document.querySelector('.footer') ||
      document.querySelector('#footer')
    )

    // Check for mobile responsiveness
    const viewport = document.querySelector('meta[name="viewport"]')
    const mobileResponsive = !!viewport

    // Estimate content width (simplified)
    const contentWidth = 1200 // Default assumption

    return {
      title,
      description,
      hasHeader,
      hasSidebar,
      hasFooter,
      contentWidth,
      mobileResponsive,
    }
  }

  /**
   * Generate ad space suggestions based on website structure
   */
  private generateAdSpaceSuggestions(document: Document, websiteInfo: any): AdSpaceSuggestion[] {
    const suggestions: AdSpaceSuggestion[] = []

    // Header banner suggestion
    if (websiteInfo.hasHeader) {
      suggestions.push({
        name: 'Header Banner',
        format: 'BANNER',
        width: 728,
        height: 90,
        position: 'header',
        selector: 'header, .header, #header',
        confidence: 0.9,
        reasoning: 'Header area detected - ideal for leaderboard banner ads'
      })
    }

    // Sidebar suggestions
    if (websiteInfo.hasSidebar) {
      suggestions.push({
        name: 'Sidebar Rectangle',
        format: 'BANNER',
        width: 300,
        height: 250,
        position: 'sidebar',
        selector: 'aside, .sidebar, #sidebar',
        confidence: 0.85,
        reasoning: 'Sidebar detected - perfect for medium rectangle ads'
      })

      suggestions.push({
        name: 'Sidebar Skyscraper',
        format: 'BANNER',
        width: 160,
        height: 600,
        position: 'sidebar',
        selector: 'aside, .sidebar, #sidebar',
        confidence: 0.7,
        reasoning: 'Sidebar suitable for vertical skyscraper ads'
      })
    }

    // Content area suggestions
    const mainContent = document.querySelector('main, .main, #main, .content, #content')
    if (mainContent) {
      suggestions.push({
        name: 'In-Content Banner',
        format: 'BANNER',
        width: 728,
        height: 90,
        position: 'content',
        selector: 'main, .main, #main, .content, #content',
        confidence: 0.8,
        reasoning: 'Main content area - good for in-article banner placement'
      })
    }

    // Footer suggestion
    if (websiteInfo.hasFooter) {
      suggestions.push({
        name: 'Footer Banner',
        format: 'BANNER',
        width: 728,
        height: 90,
        position: 'footer',
        selector: 'footer, .footer, #footer',
        confidence: 0.75,
        reasoning: 'Footer area - suitable for bottom banner ads'
      })
    }

    // Mobile-specific suggestions
    if (websiteInfo.mobileResponsive) {
      suggestions.push({
        name: 'Mobile Banner',
        format: 'BANNER',
        width: 320,
        height: 50,
        position: 'mobile-header',
        selector: 'header, .header, #header',
        confidence: 0.8,
        reasoning: 'Mobile-optimized banner for responsive websites'
      })
    }

    // Native ad suggestions for content-heavy sites
    const articles = document.querySelectorAll('article, .article, .post, .blog-post')
    if (articles.length > 0) {
      suggestions.push({
        name: 'Native Content Ad',
        format: 'NATIVE',
        width: 300,
        height: 250,
        position: 'content',
        selector: 'article, .article, .post',
        confidence: 0.85,
        reasoning: 'Content-heavy site - native ads blend well with articles'
      })
    }

    // Sort by confidence score
    return suggestions.sort((a, b) => b.confidence - a.confidence)
  }

  /**
   * Get default website info structure
   */
  private getDefaultWebsiteInfo() {
    return {
      title: 'Unknown',
      description: '',
      hasHeader: false,
      hasSidebar: false,
      hasFooter: false,
      contentWidth: 1200,
      mobileResponsive: false,
    }
  }
}

// Export singleton instance
export const websiteScanner = new WebsiteScanner()
