import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { z } from "zod"
import { AdFormat } from "@prisma/client"

const adSpaceCreateSchema = z.object({
  name: z.string().min(1, "Ad space name is required"),
  format: z.enum(["BANNER", "VIDEO", "NATIVE", "POPUP"]),
  width: z.number().min(1, "Width must be greater than 0"),
  height: z.number().min(1, "Height must be greater than 0"),
  position: z.string().min(1, "Position is required"),
})

// GET /api/publisher/ad-spaces - List all ad spaces for the publisher
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "PUBLISHER") {
      return NextResponse.json(
        { message: "Only publishers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get publisher profile
    const publisherProfile = await prisma.publisherProfile.findUnique({
      where: { userId: session.user.id },
    })

    if (!publisherProfile) {
      return NextResponse.json(
        { message: "Publisher profile not found" },
        { status: 404 }
      )
    }

    // Get ad spaces with metrics
    const adSpaces = await prisma.adSpace.findMany({
      where: { publisherId: publisherProfile.id },
      include: {
        adPlacements: {
          select: {
            impressions: true,
            clicks: true,
            conversions: true,
            revenue: true,
            cost: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    })

    // Calculate metrics for each ad space
    const adSpacesWithMetrics = adSpaces.map((adSpace) => {
      const totalImpressions = adSpace.adPlacements.reduce((sum, p) => sum + p.impressions, 0)
      const totalClicks = adSpace.adPlacements.reduce((sum, p) => sum + p.clicks, 0)
      const totalConversions = adSpace.adPlacements.reduce((sum, p) => sum + p.conversions, 0)
      const totalRevenue = adSpace.adPlacements.reduce((sum, p) => sum + p.revenue, 0)
      const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0

      return {
        id: adSpace.id,
        name: adSpace.name,
        format: adSpace.format,
        width: adSpace.width,
        height: adSpace.height,
        position: adSpace.position,
        isActive: adSpace.isActive,
        createdAt: adSpace.createdAt,
        updatedAt: adSpace.updatedAt,
        // Computed metrics
        impressions: totalImpressions,
        clicks: totalClicks,
        conversions: totalConversions,
        earnings: parseFloat(totalRevenue.toFixed(2)),
        ctr: parseFloat(ctr.toFixed(2)),
      }
    })

    return NextResponse.json({
      adSpaces: adSpacesWithMetrics,
    })
  } catch (error) {
    console.error("Ad spaces list error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/publisher/ad-spaces - Create a new ad space
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "PUBLISHER") {
      return NextResponse.json(
        { message: "Only publishers can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = adSpaceCreateSchema.parse(body)

    // Get publisher profile
    const publisherProfile = await prisma.publisherProfile.findUnique({
      where: { userId: session.user.id },
    })

    if (!publisherProfile) {
      return NextResponse.json(
        { message: "Publisher profile not found" },
        { status: 404 }
      )
    }

    // Create ad space
    const adSpace = await prisma.adSpace.create({
      data: {
        publisherId: publisherProfile.id,
        name: validatedData.name,
        format: validatedData.format as AdFormat,
        width: validatedData.width,
        height: validatedData.height,
        position: validatedData.position,
        isActive: true,
      },
    })

    return NextResponse.json(
      {
        message: "Ad space created successfully",
        adSpace: {
          id: adSpace.id,
          name: adSpace.name,
          format: adSpace.format,
          width: adSpace.width,
          height: adSpace.height,
          position: adSpace.position,
          isActive: adSpace.isActive,
          createdAt: adSpace.createdAt,
        },
      },
      { status: 201 }
    )
  } catch (error) {
    console.error("Ad space creation error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid request data", errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
