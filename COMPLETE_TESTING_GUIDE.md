# Complete Ad Network Testing Guide

This guide will walk you through testing the entire Ad Network platform from both Publisher and Advertiser perspectives, including integrating ads on your dummy HTML website.

## 🚀 Prerequisites

1. **Ad Network Platform**: Running on `http://localhost:3000`
2. **Dummy HTML Website**: Your test website where ads will be displayed
3. **Test Data**: We'll create campaigns, ads, and ad spaces during testing

---

## 📋 Part 1: Publisher Side Testing

### Step 1: Publisher Registration & Setup

1. **Register as Publisher**
   - Go to `http://localhost:3000/auth/signup`
   - Select "Publisher" role
   - Fill in details:
     - Email: `<EMAIL>`
     - Password: `Test123!`
     - Company Name: `Test Publisher Co`
     - Website URL: `http://localhost:8080` (or your dummy site URL)
     - Industry: Select any relevant industry

2. **Complete Publisher Profile**
   - After registration, complete your publisher profile
   - Add payment details (bank account info)
   - Verify your website ownership

### Step 2: Create Ad Spaces

1. **Navigate to Ad Spaces**
   - <PERSON><PERSON> as publisher
   - Go to Dashboard → Ad Spaces
   - Click "Create Ad Space"

2. **Create Multiple Ad Spaces**
   
   **Ad Space 1 - Header Banner**
   - Name: `Header Banner`
   - Format: `BANNER`
   - Width: `728`
   - Height: `90`
   - Position: `Header`
   - Status: `Active`

   **Ad Space 2 - Sidebar**
   - Name: `Sidebar Ad`
   - Format: `BANNER`
   - Width: `300`
   - Height: `250`
   - Position: `Sidebar`
   - Status: `Active`

   **Ad Space 3 - Video Ad**
   - Name: `Video Player`
   - Format: `VIDEO`
   - Width: `640`
   - Height: `360`
   - Position: `Content Area`
   - Status: `Active`

3. **Get API Keys**
   - Go to Settings → API Keys
   - Copy your Publisher API Key (you'll need this for website integration)

### Step 3: Website Integration

1. **Prepare Your HTML Website**
   Create or modify your dummy HTML website with ad containers:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Publisher Website</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .header { background: #f0f0f0; padding: 20px; margin-bottom: 20px; }
        .content { display: flex; gap: 20px; }
        .main { flex: 1; }
        .sidebar { width: 300px; }
        .ad-container { border: 2px dashed #ccc; padding: 10px; margin: 10px 0; text-align: center; }
        .video-container { margin: 20px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Test Publisher Website</h1>
        <!-- Header Ad Space -->
        <div id="header-ad" class="ad-container">
            <p>Header Ad Space (728x90)</p>
        </div>
    </div>

    <div class="content">
        <div class="main">
            <h2>Main Content</h2>
            <p>This is the main content area of the website.</p>
            
            <!-- Video Ad Space -->
            <div class="video-container">
                <div id="video-ad" class="ad-container">
                    <p>Video Ad Space (640x360)</p>
                </div>
            </div>
            
            <p>More content here...</p>
        </div>

        <div class="sidebar">
            <h3>Sidebar</h3>
            <!-- Sidebar Ad Space -->
            <div id="sidebar-ad" class="ad-container">
                <p>Sidebar Ad Space (300x250)</p>
            </div>
        </div>
    </div>

    <!-- Ad Network SDK -->
    <script src="http://localhost:3000/sdk/adnetwork-sdk.js"></script>
    <script>
        // Initialize Ad Network SDK
        AdNetwork.init({
            apiKey: 'YOUR_PUBLISHER_API_KEY', // Replace with your actual API key
            apiUrl: 'http://localhost:3000',
            debug: true
        });

        // Load ads for each ad space
        AdNetwork.loadAd('header-ad', {
            format: 'BANNER',
            width: 728,
            height: 90,
            position: 'header'
        });

        AdNetwork.loadAd('sidebar-ad', {
            format: 'BANNER',
            width: 300,
            height: 250,
            position: 'sidebar'
        });

        AdNetwork.loadAd('video-ad', {
            format: 'VIDEO',
            width: 640,
            height: 360,
            position: 'content'
        });
    </script>
</body>
</html>
```

2. **Replace API Key**
   - Replace `YOUR_PUBLISHER_API_KEY` with the actual API key from your publisher dashboard

3. **Serve Your Website**
   - Use a simple HTTP server to serve your HTML file
   - Example: `python -m http.server 8080` or `npx serve .`

---

## 💰 Part 2: Advertiser Side Testing

### Step 1: Advertiser Registration & Setup

1. **Register as Advertiser**
   - Go to `http://localhost:3000/auth/signup`
   - Select "Advertiser" role
   - Fill in details:
     - Email: `<EMAIL>`
     - Password: `Test123!`
     - Company Name: `Test Advertiser Inc`
     - Industry: Select relevant industry

2. **Add Funds to Wallet**
   - Go to Dashboard → Wallet
   - Click "Add Funds"
   - Add ₹1000 for testing (use test payment methods)

### Step 2: Create Campaigns

1. **Navigate to Campaigns**
   - Go to Dashboard → Campaigns
   - Click "Create Campaign"

2. **Create Test Campaign**
   - Name: `Test Banner Campaign`
   - Description: `Testing banner ads`
   - Budget: `₹500`
   - Daily Budget: `₹50`
   - Pricing Model: `CPC` (Cost Per Click)
   - Bid Amount: `₹2.00`
   - Target Regions: Select your region
   - Target Categories: Select relevant categories
   - Start Date: Today
   - End Date: 7 days from today
   - Click "Create Campaign"

### Step 3: Create Ads

1. **Create Banner Ad**
   - Go to the campaign you just created
   - Click "Create Ad"
   - Fill in details:
     - Title: `Test Banner Ad`
     - Description: `This is a test banner advertisement`
     - Format: `BANNER`
     - Width: `728`
     - Height: `90`
     - Image URL: `https://via.placeholder.com/728x90/4CAF50/white?text=Test+Banner+Ad`
     - Click URL: `https://example.com`
     - Status: `Active`

2. **Create Sidebar Ad**
   - Create another ad:
     - Title: `Test Sidebar Ad`
     - Description: `Sidebar advertisement`
     - Format: `BANNER`
     - Width: `300`
     - Height: `250`
     - Image URL: `https://via.placeholder.com/300x250/2196F3/white?text=Sidebar+Ad`
     - Click URL: `https://example.com`
     - Status: `Active`

3. **Create Video Ad**
   - Create a video ad:
     - Title: `Test Video Ad`
     - Description: `Video advertisement`
     - Format: `VIDEO`
     - Width: `640`
     - Height: `360`
     - Video URL: `https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4`
     - Click URL: `https://example.com`
     - Status: `Active`

### Step 4: Activate Campaign

1. **Activate Campaign**
   - Go back to your campaign
   - Click "Activate" button
   - Ensure you have:
     - ✅ Active ads
     - ✅ Sufficient balance
     - ✅ Valid targeting
     - ✅ Valid date range

---

## 🧪 Part 3: End-to-End Testing

### Step 1: Test Ad Serving

1. **Open Your Dummy Website**
   - Navigate to your HTML website (e.g., `http://localhost:8080`)
   - Check browser console for any errors
   - Verify ads are loading in the designated containers

2. **Verify Ad Display**
   - Header should show banner ads (728x90)
   - Sidebar should show square ads (300x250)
   - Video container should show video ads

### Step 2: Test Click Tracking

1. **Click on Ads**
   - Click on the displayed ads
   - Verify you're redirected to the click URL
   - Check that clicks are being tracked

2. **Monitor Analytics**
   - **Publisher Dashboard**: Check impressions and revenue
   - **Advertiser Dashboard**: Check clicks and spending

### Step 3: Test Real-Time Bidding

1. **Create Multiple Campaigns**
   - Create 2-3 campaigns with different bid amounts
   - Set different targeting criteria
   - Activate all campaigns

2. **Observe Bidding**
   - Refresh your website multiple times
   - Different ads should appear based on bidding
   - Higher bid campaigns should win more often

---

## 📊 Part 4: Monitoring & Analytics

### Publisher Monitoring

1. **Revenue Tracking**
   - Dashboard → Analytics
   - Monitor impressions, clicks, and earnings
   - Check revenue per ad space

2. **Performance Metrics**
   - CTR (Click-Through Rate)
   - RPM (Revenue Per Mille)
   - Fill rate

### Advertiser Monitoring

1. **Campaign Performance**
   - Dashboard → Campaigns → [Campaign Name]
   - Monitor impressions, clicks, and conversions
   - Track spending vs. budget

2. **ROI Analysis**
   - Cost per click (CPC)
   - Conversion tracking
   - Campaign optimization suggestions

---

## 🔧 Part 5: Advanced Testing

### Test Fraud Prevention

1. **Rapid Clicking**
   - Try clicking the same ad multiple times quickly
   - System should detect and prevent fraudulent clicks

2. **IP-based Filtering**
   - Test from different IP addresses
   - Verify proper tracking and filtering

### Test Campaign Lifecycle

1. **Budget Exhaustion**
   - Set a low budget (₹10)
   - Let campaign run until budget is exhausted
   - Verify automatic pause/completion

2. **Date-based Expiration**
   - Create campaign with end date tomorrow
   - Wait for automatic status change to PAUSED

### Test Error Scenarios

1. **Insufficient Balance**
   - Try activating campaign with ₹0 balance
   - Verify proper error handling

2. **Invalid Ad Formats**
   - Try loading ads with mismatched dimensions
   - Test error handling and fallbacks

---

## 📝 Testing Checklist

### Publisher Side ✅
- [ ] Registration and profile setup
- [ ] Ad space creation (multiple formats)
- [ ] API key generation
- [ ] Website integration
- [ ] Ad display verification
- [ ] Revenue tracking
- [ ] Analytics monitoring

### Advertiser Side ✅
- [ ] Registration and profile setup
- [ ] Wallet funding
- [ ] Campaign creation
- [ ] Ad creation (multiple formats)
- [ ] Campaign activation
- [ ] Performance monitoring
- [ ] Budget management

### Integration Testing ✅
- [ ] Real-time bidding
- [ ] Click tracking
- [ ] Impression tracking
- [ ] Fraud prevention
- [ ] Campaign lifecycle
- [ ] Error handling
- [ ] Analytics accuracy

---

## 🚨 Troubleshooting

### Common Issues

1. **Ads Not Loading**
   - Check API key validity
   - Verify SDK script loading
   - Check browser console for errors
   - Ensure ad spaces are active

2. **No Bids/Empty Ad Spaces**
   - Verify campaigns are active
   - Check targeting criteria match
   - Ensure sufficient advertiser balance
   - Verify ad formats match ad spaces

3. **Tracking Issues**
   - Check network requests in browser dev tools
   - Verify API endpoints are responding
   - Check database for recorded events

### Debug Mode

Enable debug mode in SDK:
```javascript
AdNetwork.init({
    apiKey: 'your-api-key',
    debug: true // This will show detailed logs
});
```

---

---

## 🎯 Part 6: Advanced Pacing & Click Tracking Testing

### Enhanced Click Tracking Features

Your ad network now includes advanced click tracking with:
- **Fraud Detection**: IP-based, rapid clicking, bot detection
- **Fair Revenue Split**: Dynamic revenue sharing based on performance
- **Device & Geo Tracking**: Comprehensive user analytics
- **Quality Scoring**: Performance-based ad space evaluation

### Advertiser Pacing Features

- **Budget Pacing**: Even, front-loaded, back-loaded, performance-based
- **Bid Optimization**: Automatic bid adjustments based on CTR/conversion
- **Performance Targets**: Set target CTR, conversion rate, CPA, ROAS
- **Real-time Monitoring**: Budget exhaustion alerts and recommendations

### Publisher Pacing Features

- **Revenue Pacing**: Optimize daily/weekly/monthly revenue distribution
- **Ad Serving Strategy**: Revenue-optimized, quality-optimized, balanced
- **Fill Rate Optimization**: Automatic ad space performance monitoring
- **Quality Control**: Block low-performing ads automatically

### Testing Enhanced Click Tracking

1. **Test Fraud Detection**
   ```javascript
   // In your test HTML, try rapid clicking
   for (let i = 0; i < 10; i++) {
     setTimeout(() => {
       document.getElementById('header-ad').click();
     }, i * 100); // Click every 100ms
   }
   ```

   **Expected Result**: After 2-3 clicks, subsequent clicks should be blocked as fraudulent.

2. **Test Revenue Split Calculation**
   - Click on ads and check server logs for revenue calculations
   - Look for messages like: `Publisher revenue: ₹1.40, Platform revenue: ₹0.60`

3. **Monitor Click Quality**
   - Check `/api/publisher/pacing` endpoint for quality scores
   - High-quality clicks should improve publisher revenue share

### Testing Advertiser Pacing

1. **Access Pacing Settings**
   ```bash
   # Get current pacing settings
   curl -X GET http://localhost:3000/api/advertiser/pacing \
     -H "Cookie: your-session-cookie"
   ```

2. **Update Pacing Configuration**
   ```bash
   curl -X PUT http://localhost:3000/api/advertiser/pacing \
     -H "Content-Type: application/json" \
     -H "Cookie: your-session-cookie" \
     -d '{
       "bidPacingStrategy": "PERFORMANCE_BASED",
       "targetCTR": 3.0,
       "targetConversionRate": 8.0,
       "maxBidIncrease": 0.4,
       "maxBidDecrease": 0.2
     }'
   ```

3. **Monitor Automatic Bid Adjustments**
   - Watch server logs for bid optimization messages
   - Look for: `Campaign X: ₹2.00 -> ₹2.20 (High performance - increasing bid)`

4. **Test Budget Pacing**
   - Create campaign with small budget (₹50)
   - Monitor spending throughout the day
   - Check for even distribution vs. front-loaded spending

### Testing Publisher Pacing

1. **Access Publisher Insights**
   ```bash
   curl -X GET http://localhost:3000/api/publisher/pacing \
     -H "Cookie: your-publisher-session-cookie"
   ```

2. **Configure Revenue Targets**
   ```bash
   curl -X PUT http://localhost:3000/api/publisher/pacing \
     -H "Content-Type: application/json" \
     -H "Cookie: your-publisher-session-cookie" \
     -d '{
       "dailyRevenueTarget": 100,
       "adServingStrategy": "REVENUE_OPTIMIZED",
       "minFillRate": 0.85,
       "minAdQualityScore": 0.8
     }'
   ```

3. **Monitor Ad Space Performance**
   - Check which ad spaces have highest RPM
   - Verify low-performing spaces get flagged
   - Test automatic ad space disabling for very poor performance

### Real-World Pacing Scenarios

#### Scenario 1: High-Performance Campaign
```
Initial Bid: ₹2.00
CTR: 4.5% (Target: 2.0%)
Conversion Rate: 12% (Target: 5.0%)

Expected Pacing Action:
✅ Increase bid to ₹2.40 (20% increase)
✅ Reason: "High performance - increasing bid"
✅ Confidence: 90%
```

#### Scenario 2: Budget Overspending
```
Campaign Budget: ₹1000
Time Elapsed: 30% of campaign duration
Budget Spent: 60% (₹600)

Expected Pacing Action:
⚠️ Decrease bid by 15%
⚠️ Reason: "Overspending - decreasing bid"
⚠️ Alert: "Campaign at risk of budget exhaustion"
```

#### Scenario 3: Low Publisher Fill Rate
```
Ad Space Fill Rate: 65% (Target: 80%)
Quality Score: 0.9
RPM: ₹45

Expected Pacing Action:
📈 Switch to "FILL_RATE_OPTIMIZED" strategy
📈 Recommendation: "Consider adjusting ad space dimensions"
📈 Maintain high quality score
```

### Performance Monitoring Dashboard

Create a simple monitoring dashboard to track pacing metrics:

```html
<!DOCTYPE html>
<html>
<head>
    <title>Pacing Monitor</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <h1>Ad Network Pacing Monitor</h1>

    <div id="advertiser-metrics">
        <h2>Advertiser Metrics</h2>
        <canvas id="bidChart"></canvas>
        <canvas id="performanceChart"></canvas>
    </div>

    <div id="publisher-metrics">
        <h2>Publisher Metrics</h2>
        <canvas id="revenueChart"></canvas>
        <canvas id="fillRateChart"></canvas>
    </div>

    <script>
        // Fetch and display real-time pacing data
        async function updateMetrics() {
            try {
                const advertiserData = await fetch('/api/advertiser/pacing').then(r => r.json());
                const publisherData = await fetch('/api/publisher/pacing').then(r => r.json());

                // Update charts with real data
                updateBidChart(advertiserData);
                updateRevenueChart(publisherData);

            } catch (error) {
                console.error('Failed to fetch metrics:', error);
            }
        }

        // Update every 30 seconds
        setInterval(updateMetrics, 30000);
        updateMetrics(); // Initial load
    </script>
</body>
</html>
```

### Expected Log Messages

When testing, you should see these types of log messages:

**Enhanced Click Tracking:**
```
[Enhanced Click Tracker] Valid click recorded: trackingId=abc123
[Enhanced Click Tracker] Revenue split: Publisher=₹1.40, Platform=₹0.60
[Enhanced Click Tracker] Fraud detected: Rapid clicking from IP *************
```

**Advertiser Pacing:**
```
[Advertiser Pacing] Campaign 123: ₹2.00 -> ₹2.30 (High performance)
[Advertiser Pacing] ⚠️ Campaign 456 at 90% budget utilization
[Advertiser Pacing] 🛑 Campaign 789 auto-paused due to budget exhaustion
```

**Publisher Pacing:**
```
[Publisher Pacing] Publisher ABC revenue pacing changed to AGGRESSIVE
[Publisher Pacing] ⚠️ Publisher XYZ fill rate below target: 72.5%
[Publisher Pacing] 🚀 Publisher DEF has 3 high-performing ad spaces
```

---

This comprehensive guide covers all aspects of testing your Ad Network platform. Follow each step carefully and document any issues you encounter for further debugging.
