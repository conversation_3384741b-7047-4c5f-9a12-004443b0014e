// "use client"

// import { useSession } from "next-auth/react"
// import { useRouter } from "next/navigation"
// import { useEffect } from "react"
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
// import { Button } from "@/components/ui/button"
// import { Loader2, Users, Megaphone, BarChart3, Settings } from "lucide-react"

// export default function DashboardPage() {
//   const { data: session, status } = useSession()
//   const router = useRouter()

//   useEffect(() => {
//     if (status === "loading") return // Still loading

//     if (!session) {
//       router.push("/auth/signin")
//       return
//     }

//     if (!session.user.isOnboarded) {
//       // Redirect to onboarding if not completed
//       if (session.user.role === "PUBLISHER") {
//         router.push("/onboarding/publisher")
//       } else if (session.user.role === "ADVERTISER") {
//         router.push("/onboarding/advertiser")
//       }
//       return
//     }

//     // Redirect to role-specific dashboard
//     if (session.user.role === "PUBLISHER") {
//       router.push("/dashboard/publisher")
//     } else if (session.user.role === "ADVERTISER") {
//       router.push("/dashboard/advertiser")
//     } else if (session.user.role === "ADMIN") {
//       router.push("/dashboard/admin")
//     }
//   }, [session, status, router])

//   if (status === "loading") {
//     return (
//       <div className="min-h-screen flex items-center justify-center">
//         <Loader2 className="h-8 w-8 animate-spin" />
//       </div>
//     )
//   }

//   if (!session) {
//     return null // Will redirect to signin
//   }

//   return (
//     <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
//       <div className="max-w-4xl mx-auto">
//         <div className="text-center mb-8">
//           <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
//             Welcome to Ad Network Platform
//           </h1>
//           <p className="mt-2 text-gray-600 dark:text-gray-400">
//             Redirecting you to your dashboard...
//           </p>
//         </div>

//         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
//           <Card>
//             <CardHeader>
//               <CardTitle className="flex items-center gap-2">
//                 <Users className="h-5 w-5 text-blue-500" />
//                 Publishers
//               </CardTitle>
//               <CardDescription>
//                 Monetize your website traffic
//               </CardDescription>
//             </CardHeader>
//             <CardContent>
//               <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
//                 Connect with advertisers and start earning revenue from your website.
//               </p>
//               <Button variant="outline" className="w-full">
//                 Learn More
//               </Button>
//             </CardContent>
//           </Card>

//           <Card>
//             <CardHeader>
//               <CardTitle className="flex items-center gap-2">
//                 <Megaphone className="h-5 w-5 text-green-500" />
//                 Advertisers
//               </CardTitle>
//               <CardDescription>
//                 Reach your target audience
//               </CardDescription>
//             </CardHeader>
//             <CardContent>
//               <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
//                 Create campaigns and reach customers on relevant websites.
//               </p>
//               <Button variant="outline" className="w-full">
//                 Learn More
//               </Button>
//             </CardContent>
//           </Card>

//           <Card>
//             <CardHeader>
//               <CardTitle className="flex items-center gap-2">
//                 <BarChart3 className="h-5 w-5 text-purple-500" />
//                 Analytics
//               </CardTitle>
//               <CardDescription>
//                 Track your performance
//               </CardDescription>
//             </CardHeader>
//             <CardContent>
//               <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
//                 Monitor clicks, impressions, and revenue in real-time.
//               </p>
//               <Button variant="outline" className="w-full">
//                 View Analytics
//               </Button>
//             </CardContent>
//           </Card>
//         </div>
//       </div>
//     </div>
//   )
// }



import { AppSidebar } from "@/components/app-sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"

export default function Page() {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="#">
                    Building Your Application
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Data Fetching</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <div className="grid auto-rows-min gap-4 md:grid-cols-3">
            <div className="bg-muted/50 aspect-video rounded-xl" />
            <div className="bg-muted/50 aspect-video rounded-xl" />
            <div className="bg-muted/50 aspect-video rounded-xl" />
          </div>
          <div className="bg-muted/50 min-h-[100vh] flex-1 rounded-xl md:min-h-min" />
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
