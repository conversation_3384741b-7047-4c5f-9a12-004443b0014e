import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { websiteScanner } from "@/lib/website-scanner"
import { z } from "zod"

const scanRequestSchema = z.object({
  url: z.string().url("Invalid URL format"),
  autoCreate: z.boolean().optional().default(false),
})

// POST /api/publisher/scan-website - Scan website for ad space suggestions
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "PUBLISHER") {
      return NextResponse.json(
        { message: "Only publishers can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = scanRequestSchema.parse(body)

    // Get publisher profile
    const publisherProfile = await prisma.publisherProfile.findUnique({
      where: { userId: session.user.id },
    })

    if (!publisherProfile) {
      return NextResponse.json(
        { message: "Publisher profile not found" },
        { status: 404 }
      )
    }

    // Verify the URL belongs to the publisher (security check)
    const publisherDomain = new URL(publisherProfile.websiteUrl).hostname
    const scanDomain = new URL(validatedData.url).hostname
    
    if (publisherDomain !== scanDomain) {
      return NextResponse.json(
        { 
          message: "URL domain does not match your registered website",
          registeredDomain: publisherDomain,
          requestedDomain: scanDomain
        },
        { status: 403 }
      )
    }

    // Perform website scan
    console.log(`Scanning website: ${validatedData.url}`)
    const scanResult = await websiteScanner.scanWebsite(validatedData.url)

    if (!scanResult.success) {
      return NextResponse.json(
        { 
          message: "Failed to scan website",
          error: scanResult.error,
          suggestions: []
        },
        { status: 400 }
      )
    }

    // If autoCreate is true, automatically create the suggested ad spaces
    let createdAdSpaces = []
    if (validatedData.autoCreate && scanResult.suggestions.length > 0) {
      try {
        // Filter suggestions with high confidence (>= 0.8)
        const highConfidenceSuggestions = scanResult.suggestions.filter(s => s.confidence >= 0.8)
        
        for (const suggestion of highConfidenceSuggestions) {
          // Check if ad space with similar name already exists
          const existingAdSpace = await prisma.adSpace.findFirst({
            where: {
              publisherId: publisherProfile.id,
              name: suggestion.name,
            },
          })

          if (!existingAdSpace) {
            const createdAdSpace = await prisma.adSpace.create({
              data: {
                publisherId: publisherProfile.id,
                name: suggestion.name,
                format: suggestion.format,
                width: suggestion.width,
                height: suggestion.height,
                position: suggestion.position,
                isActive: false, // Created as inactive by default
              },
            })
            createdAdSpaces.push(createdAdSpace)
          }
        }
      } catch (error) {
        console.error("Error auto-creating ad spaces:", error)
        // Continue without auto-creation if there's an error
      }
    }

    return NextResponse.json({
      success: true,
      websiteInfo: scanResult.websiteInfo,
      suggestions: scanResult.suggestions,
      autoCreated: createdAdSpaces.length,
      createdAdSpaces: createdAdSpaces.map(space => ({
        id: space.id,
        name: space.name,
        format: space.format,
        width: space.width,
        height: space.height,
        position: space.position,
      })),
      message: createdAdSpaces.length > 0 
        ? `Successfully scanned website and created ${createdAdSpaces.length} ad spaces`
        : "Website scanned successfully"
    })
  } catch (error) {
    console.error("Website scan error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid request data", errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// GET /api/publisher/scan-website - Get scan history or status
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "PUBLISHER") {
      return NextResponse.json(
        { message: "Only publishers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get publisher profile
    const publisherProfile = await prisma.publisherProfile.findUnique({
      where: { userId: session.user.id },
      include: {
        adSpaces: {
          select: {
            id: true,
            name: true,
            format: true,
            width: true,
            height: true,
            position: true,
            isActive: true,
            createdAt: true,
          },
          orderBy: { createdAt: "desc" },
        },
      },
    })

    if (!publisherProfile) {
      return NextResponse.json(
        { message: "Publisher profile not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({
      websiteUrl: publisherProfile.websiteUrl,
      totalAdSpaces: publisherProfile.adSpaces.length,
      activeAdSpaces: publisherProfile.adSpaces.filter(space => space.isActive).length,
      recentAdSpaces: publisherProfile.adSpaces.slice(0, 5),
      canScan: true,
      lastScanDate: null, // TODO: Implement scan history tracking
    })
  } catch (error) {
    console.error("Scan status error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
