import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// POST /api/admin/campaigns/update-expired - Manually update expired campaigns
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    // Allow both admin and advertiser to trigger this (advertisers can only see their own campaigns)
    if (session.user.role !== "ADMIN" && session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only admins and advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    const now = new Date()
    
    // Build where clause based on user role
    const whereClause: any = {
      status: "ACTIVE",
      endDate: {
        lt: now
      }
    }

    // If advertiser, only update their own campaigns
    if (session.user.role === "ADVERTISER") {
      const advertiserProfile = await prisma.advertiserProfile.findUnique({
        where: { userId: session.user.id }
      })

      if (!advertiserProfile) {
        return NextResponse.json(
          { message: "Advertiser profile not found" },
          { status: 404 }
        )
      }

      whereClause.advertiserId = advertiserProfile.id
    }

    // Find active campaigns that have passed their end date
    const expiredCampaigns = await prisma.campaign.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        endDate: true,
        advertiser: {
          select: {
            id: true,
            companyName: true,
            balance: true
          }
        }
      }
    })

    const updatedCampaigns = []

    // Update expired campaigns to PAUSED status (so they can be reactivated if end date is extended)
    for (const campaign of expiredCampaigns) {
      const updatedCampaign = await prisma.campaign.update({
        where: { id: campaign.id },
        data: {
          status: "PAUSED",
          updatedAt: now
        },
        select: {
          id: true,
          name: true,
          status: true,
          endDate: true,
          updatedAt: true
        }
      })

      // Log the pause in wallet transactions for audit trail
      await prisma.walletTransaction.create({
        data: {
          advertiserId: campaign.advertiser.id,
          type: "CREDIT", // No actual credit, just logging
          amount: 0,
          description: `Campaign "${campaign.name}" automatically paused (end date reached)`,
          balanceAfter: campaign.advertiser.balance,
          relatedCampaignId: campaign.id,
        },
      })

      updatedCampaigns.push(updatedCampaign)
    }

    return NextResponse.json({
      success: true,
      message: `Updated ${updatedCampaigns.length} expired campaigns to PAUSED status`,
      updatedCampaigns,
      totalUpdated: updatedCampaigns.length
    })

  } catch (error) {
    console.error("Error updating expired campaigns:", error)
    
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// GET /api/admin/campaigns/update-expired - Check for expired campaigns without updating
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADMIN" && session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only admins and advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    const now = new Date()
    
    // Build where clause based on user role
    const whereClause: any = {
      status: "ACTIVE",
      endDate: {
        lt: now
      }
    }

    // If advertiser, only check their own campaigns
    if (session.user.role === "ADVERTISER") {
      const advertiserProfile = await prisma.advertiserProfile.findUnique({
        where: { userId: session.user.id }
      })

      if (!advertiserProfile) {
        return NextResponse.json(
          { message: "Advertiser profile not found" },
          { status: 404 }
        )
      }

      whereClause.advertiserId = advertiserProfile.id
    }

    // Find active campaigns that have passed their end date
    const expiredCampaigns = await prisma.campaign.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        endDate: true,
        status: true,
        advertiser: {
          select: {
            companyName: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      expiredCampaigns,
      totalExpired: expiredCampaigns.length,
      message: expiredCampaigns.length > 0 
        ? `Found ${expiredCampaigns.length} expired campaigns that need to be updated`
        : "No expired campaigns found"
    })

  } catch (error) {
    console.error("Error checking expired campaigns:", error)
    
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
