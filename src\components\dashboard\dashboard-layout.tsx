"use client"

import { useState } from "react"
import { useSession, signOut } from "next-auth/react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import {
  BarChart3,
  DollarSign,
  Globe,
  Key,
  Menu,
  Settings,
  Users,
  LogOut,
  Home,
  PlusCircle,
  Target,
  TrendingUp
} from "lucide-react"
import { cn } from "@/lib/utils"

interface DashboardLayoutProps {
  children: React.ReactNode
  navigation?: Array<{
    name: string
    href: string
    icon: any
    disabled?: boolean
  }>
  userRole?: string
  title?: string
}

const publisherNavItems = [
  {
    title: "Overview",
    href: "/dashboard/publisher",
    icon: Home,
  },
  {
    title: "Analytics",
    href: "/dashboard/publisher/analytics",
    icon: BarChart3,
  },
  {
    title: "Pacing",
    href: "/dashboard/publisher/pacing",
    icon: TrendingUp,
  },
  {
    title: "Ad Spaces",
    href: "/dashboard/publisher/ad-spaces",
    icon: Globe,
  },
  {
    title: "Earnings",
    href: "/dashboard/publisher/earnings",
    icon: DollarSign,
  },
  {
    title: "API Keys",
    href: "/dashboard/publisher/api-keys",
    icon: Key,
  },
  {
    title: "Settings",
    href: "/dashboard/publisher/settings",
    icon: Settings,
  },
]

const advertiserNavItems = [
  {
    title: "Overview",
    href: "/dashboard/advertiser",
    icon: Home,
  },
  {
    title: "Campaigns",
    href: "/dashboard/advertiser/campaigns",
    icon: Target,
  },
  {
    title: "Ads",
    href: "/dashboard/advertiser/ads",
    icon: PlusCircle,
  },
  {
    title: "Analytics",
    href: "/dashboard/advertiser/analytics",
    icon: BarChart3,
  },
  {
    title: "Billing",
    href: "/dashboard/advertiser/billing",
    icon: DollarSign,
  },
  {
    title: "Settings",
    href: "/dashboard/advertiser/settings",
    icon: Settings,
  },
]

export function DashboardLayout({ children, navigation, userRole, title }: DashboardLayoutProps) {
  const { data: session } = useSession()
  const pathname = usePathname()
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const handleSignOut = () => {
    signOut({ callbackUrl: "/" })
  }

  // Determine which navigation to use
  const navItems = navigation || (session?.user?.role === "ADVERTISER" ? advertiserNavItems : publisherNavItems)
  const dashboardTitle = title || (session?.user?.role === "ADVERTISER" ? "Advertiser Dashboard" : "Publisher Dashboard")
  const dashboardHref = session?.user?.role === "ADVERTISER" ? "/dashboard/advertiser" : "/dashboard/publisher"

  const SidebarContent = () => (
    <div className="flex h-full flex-col">
      <div className="flex h-14 items-center border-b px-4 lg:h-[60px] lg:px-6">
        <Link href={dashboardHref} className="flex items-center gap-2 font-semibold">
          <Globe className="h-6 w-6" />
          <span>{dashboardTitle}</span>
        </Link>
      </div>
      <div className="flex-1">
        <nav className="grid items-start px-2 text-sm font-medium lg:px-4">
          {navItems.map((item: any) => (
            <Link
              key={item.href}
              href={item.disabled ? "#" : item.href}
              className={cn(
                "flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",
                pathname === item.href && "bg-muted text-primary",
                item.disabled && "opacity-50 cursor-not-allowed"
              )}
              onClick={() => setSidebarOpen(false)}
            >
              <item.icon className="h-4 w-4" />
              {item.name || item.title}
            </Link>
          ))}
        </nav>
      </div>
    </div>
  )

  return (
    <div className="grid min-h-screen w-full md:grid-cols-[220px_1fr] lg:grid-cols-[280px_1fr]">
      {/* Desktop Sidebar */}
      <div className="hidden border-r bg-muted/40 md:block">
        <SidebarContent />
      </div>

      <div className="flex flex-col">
        {/* Header */}
        <header className="flex h-14 items-center gap-4 border-b bg-muted/40 px-4 lg:h-[60px] lg:px-6">
          {/* Mobile Menu */}
          <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="shrink-0 md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="flex flex-col">
              <SidebarContent />
            </SheetContent>
          </Sheet>

          <div className="w-full flex-1">
            <h1 className="text-lg font-semibold md:text-2xl">
              Welcome back, {session?.user?.name}
            </h1>
          </div>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="secondary" size="icon" className="rounded-full">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={session?.user?.image || ""} alt={session?.user?.name || ""} />
                  <AvatarFallback>
                    {session?.user?.name?.charAt(0)?.toUpperCase() || "U"}
                  </AvatarFallback>
                </Avatar>
                <span className="sr-only">Toggle user menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/dashboard/publisher/settings">
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut}>
                <LogOut className="mr-2 h-4 w-4" />
                Logout
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </header>

        {/* Main Content */}
        <main className="flex flex-1 flex-col gap-4 p-4 lg:gap-6 lg:p-6">
          {children}
        </main>
      </div>
    </div>
  )
}
