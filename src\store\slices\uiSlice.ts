import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface UIState {
  // Modal states
  isCreateAdSpaceDialogOpen: boolean
  isScanDialogOpen: boolean
  isCreateCampaignDialogOpen: boolean
  isCreateAdDialogOpen: boolean
  isEditCampaignDialogOpen: boolean
  isEditAdDialogOpen: boolean
  
  // Loading states
  isGlobalLoading: boolean
  
  // Toast/notification state
  notifications: Array<{
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    message: string
    timestamp: number
  }>
  
  // Search and filter states
  searchTerms: {
    campaigns: string
    ads: string
    adSpaces: string
  }
  
  filters: {
    campaigns: {
      status: string
      pricingModel: string
    }
    ads: {
      format: string
      status: string
    }
    adSpaces: {
      format: string
      isActive: string
    }
  }
  
  // Selected items for bulk operations
  selectedItems: {
    campaigns: string[]
    ads: string[]
    adSpaces: string[]
  }
}

const initialState: UIState = {
  // Modal states
  isCreateAdSpaceDialogOpen: false,
  isScanDialogOpen: false,
  isCreateCampaignDialogOpen: false,
  isCreateAdDialogOpen: false,
  isEditCampaignDialogOpen: false,
  isEditAdDialogOpen: false,
  
  // Loading states
  isGlobalLoading: false,
  
  // Notifications
  notifications: [],
  
  // Search and filter states
  searchTerms: {
    campaigns: '',
    ads: '',
    adSpaces: '',
  },
  
  filters: {
    campaigns: {
      status: 'all',
      pricingModel: 'all',
    },
    ads: {
      format: 'all',
      status: 'all',
    },
    adSpaces: {
      format: 'all',
      isActive: 'all',
    },
  },
  
  selectedItems: {
    campaigns: [],
    ads: [],
    adSpaces: [],
  },
}

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Modal actions
    setCreateAdSpaceDialogOpen: (state, action: PayloadAction<boolean>) => {
      state.isCreateAdSpaceDialogOpen = action.payload
    },
    setScanDialogOpen: (state, action: PayloadAction<boolean>) => {
      state.isScanDialogOpen = action.payload
    },
    setCreateCampaignDialogOpen: (state, action: PayloadAction<boolean>) => {
      state.isCreateCampaignDialogOpen = action.payload
    },
    setCreateAdDialogOpen: (state, action: PayloadAction<boolean>) => {
      state.isCreateAdDialogOpen = action.payload
    },
    setEditCampaignDialogOpen: (state, action: PayloadAction<boolean>) => {
      state.isEditCampaignDialogOpen = action.payload
    },
    setEditAdDialogOpen: (state, action: PayloadAction<boolean>) => {
      state.isEditAdDialogOpen = action.payload
    },
    
    // Loading actions
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.isGlobalLoading = action.payload
    },
    
    // Notification actions
    addNotification: (state, action: PayloadAction<Omit<UIState['notifications'][0], 'id' | 'timestamp'>>) => {
      const notification = {
        ...action.payload,
        id: Date.now().toString(),
        timestamp: Date.now(),
      }
      state.notifications.push(notification)
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload)
    },
    clearNotifications: (state) => {
      state.notifications = []
    },
    
    // Search actions
    setSearchTerm: (state, action: PayloadAction<{ type: keyof UIState['searchTerms']; term: string }>) => {
      state.searchTerms[action.payload.type] = action.payload.term
    },
    
    // Filter actions
    setFilter: (state, action: PayloadAction<{ 
      type: keyof UIState['filters']
      filterKey: string
      value: string 
    }>) => {
      const { type, filterKey, value } = action.payload
      if (state.filters[type]) {
        (state.filters[type] as any)[filterKey] = value
      }
    },
    
    clearFilters: (state, action: PayloadAction<keyof UIState['filters']>) => {
      const type = action.payload
      if (type === 'campaigns') {
        state.filters.campaigns = { status: 'all', pricingModel: 'all' }
      } else if (type === 'ads') {
        state.filters.ads = { format: 'all', status: 'all' }
      } else if (type === 'adSpaces') {
        state.filters.adSpaces = { format: 'all', isActive: 'all' }
      }
    },
    
    // Selection actions
    selectItem: (state, action: PayloadAction<{ type: keyof UIState['selectedItems']; id: string }>) => {
      const { type, id } = action.payload
      if (!state.selectedItems[type].includes(id)) {
        state.selectedItems[type].push(id)
      }
    },
    
    deselectItem: (state, action: PayloadAction<{ type: keyof UIState['selectedItems']; id: string }>) => {
      const { type, id } = action.payload
      state.selectedItems[type] = state.selectedItems[type].filter(itemId => itemId !== id)
    },
    
    selectAllItems: (state, action: PayloadAction<{ type: keyof UIState['selectedItems']; ids: string[] }>) => {
      const { type, ids } = action.payload
      state.selectedItems[type] = ids
    },
    
    clearSelection: (state, action: PayloadAction<keyof UIState['selectedItems']>) => {
      state.selectedItems[action.payload] = []
    },
    
    // Bulk actions
    toggleItemSelection: (state, action: PayloadAction<{ type: keyof UIState['selectedItems']; id: string }>) => {
      const { type, id } = action.payload
      const index = state.selectedItems[type].indexOf(id)
      if (index === -1) {
        state.selectedItems[type].push(id)
      } else {
        state.selectedItems[type].splice(index, 1)
      }
    },
  },
})

export const {
  // Modal actions
  setCreateAdSpaceDialogOpen,
  setScanDialogOpen,
  setCreateCampaignDialogOpen,
  setCreateAdDialogOpen,
  setEditCampaignDialogOpen,
  setEditAdDialogOpen,
  
  // Loading actions
  setGlobalLoading,
  
  // Notification actions
  addNotification,
  removeNotification,
  clearNotifications,
  
  // Search actions
  setSearchTerm,
  
  // Filter actions
  setFilter,
  clearFilters,
  
  // Selection actions
  selectItem,
  deselectItem,
  selectAllItems,
  clearSelection,
  toggleItemSelection,
} = uiSlice.actions

export default uiSlice.reducer
