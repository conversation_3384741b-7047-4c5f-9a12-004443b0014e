import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { z } from "zod"
import { PricingModel } from "@prisma/client"

const campaignUpdateSchema = z.object({
  name: z.string().min(1, "Campaign name is required").optional(),
  description: z.string().optional(),
  budget: z.number().min(1, "Budget must be greater than 0").optional(),
  dailyBudget: z.number().min(1, "Daily budget must be greater than 0").optional(),
  pricingModel: z.enum(["CPC", "CPM", "CPA"]).optional(),
  bidAmount: z.number().min(0.01, "Bid amount must be greater than 0").optional(),
  targetRegions: z.array(z.string()).min(1, "At least one target region is required").optional(),
  targetCategories: z.array(z.string()).min(1, "At least one target category is required").optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// GET /api/advertiser/campaigns/[id] - Get specific campaign details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get campaign with full details
    const campaign = await prisma.campaign.findUnique({
      where: { id: params.id },
      include: {
        advertiser: {
          include: {
            user: true,
          },
        },
        ads: {
          include: {
            adPlacements: {
              select: {
                impressions: true,
                clicks: true,
                conversions: true,
                cost: true,
              },
            },
          },
        },
        adPlacements: {
          include: {
            adSpace: {
              include: {
                publisher: {
                  select: {
                    websiteName: true,
                    websiteUrl: true,
                  },
                },
              },
            },
          },
        },
      },
    })

    if (!campaign) {
      return NextResponse.json(
        { message: "Campaign not found" },
        { status: 404 }
      )
    }

    // Check if user owns this campaign
    if (campaign.advertiser.user.id !== session.user.id) {
      return NextResponse.json(
        { message: "Forbidden" },
        { status: 403 }
      )
    }

    // Calculate campaign metrics
    const totalImpressions = campaign.adPlacements.reduce((sum, p) => sum + p.impressions, 0)
    const totalClicks = campaign.adPlacements.reduce((sum, p) => sum + p.clicks, 0)
    const totalConversions = campaign.adPlacements.reduce((sum, p) => sum + p.conversions, 0)
    const totalSpent = campaign.adPlacements.reduce((sum, p) => sum + p.cost, 0)
    const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0
    const costPerClick = totalClicks > 0 ? totalSpent / totalClicks : 0
    const costPerConversion = totalConversions > 0 ? totalSpent / totalConversions : 0

    // Calculate ad metrics
    const adsWithMetrics = campaign.ads.map((ad) => {
      const adImpressions = ad.adPlacements.reduce((sum, p) => sum + p.impressions, 0)
      const adClicks = ad.adPlacements.reduce((sum, p) => sum + p.clicks, 0)
      const adConversions = ad.adPlacements.reduce((sum, p) => sum + p.conversions, 0)
      const adCost = ad.adPlacements.reduce((sum, p) => sum + p.cost, 0)
      const adCtr = adImpressions > 0 ? (adClicks / adImpressions) * 100 : 0
      const adCostPerClick = adClicks > 0 ? adCost / adClicks : 0

      return {
        ...ad,
        impressions: adImpressions,
        clicks: adClicks,
        conversions: adConversions,
        ctr: parseFloat(adCtr.toFixed(2)),
        costPerClick: parseFloat(adCostPerClick.toFixed(2)),
        adPlacements: undefined, // Remove to avoid circular reference
      }
    })

    const campaignWithMetrics = {
      ...campaign,
      spent: parseFloat(totalSpent.toFixed(2)),
      impressions: totalImpressions,
      clicks: totalClicks,
      conversions: totalConversions,
      ctr: parseFloat(ctr.toFixed(2)),
      costPerClick: parseFloat(costPerClick.toFixed(2)),
      costPerConversion: parseFloat(costPerConversion.toFixed(2)),
      remainingBudget: parseFloat((campaign.budget - totalSpent).toFixed(2)),
      ads: adsWithMetrics,
      placements: campaign.adPlacements.map((placement) => ({
        id: placement.id,
        adSpaceName: placement.adSpace.name,
        publisherName: placement.adSpace.publisher.websiteName,
        publisherUrl: placement.adSpace.publisher.websiteUrl,
        impressions: placement.impressions,
        clicks: placement.clicks,
        conversions: placement.conversions,
        cost: placement.cost,
        isActive: placement.isActive,
      })),
      advertiser: undefined, // Remove sensitive data
      adPlacements: undefined, // Remove to avoid duplication
    }

    return NextResponse.json({
      campaign: campaignWithMetrics,
    })
  } catch (error) {
    console.error("Campaign details error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/advertiser/campaigns/[id] - Update campaign
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = campaignUpdateSchema.parse(body)

    // Get campaign and verify ownership
    const campaign = await prisma.campaign.findUnique({
      where: { id: params.id },
      include: {
        advertiser: {
          include: {
            user: true,
          },
        },
      },
    })

    if (!campaign) {
      return NextResponse.json(
        { message: "Campaign not found" },
        { status: 404 }
      )
    }

    // Check if user owns this campaign
    if (campaign.advertiser.user.id !== session.user.id) {
      return NextResponse.json(
        { message: "Forbidden" },
        { status: 403 }
      )
    }

    // Check if campaign can be updated (not if it's completed or cancelled)
    if (campaign.status === "COMPLETED" || campaign.status === "CANCELLED") {
      return NextResponse.json(
        { message: "Cannot update completed or cancelled campaigns" },
        { status: 400 }
      )
    }

    // Update campaign
    const updatedCampaign = await prisma.campaign.update({
      where: { id: params.id },
      data: {
        ...(validatedData.name && { name: validatedData.name }),
        ...(validatedData.description !== undefined && { description: validatedData.description }),
        ...(validatedData.budget && { budget: validatedData.budget }),
        ...(validatedData.dailyBudget && { dailyBudget: validatedData.dailyBudget }),
        ...(validatedData.pricingModel && { pricingModel: validatedData.pricingModel as PricingModel }),
        ...(validatedData.bidAmount && { bidAmount: validatedData.bidAmount }),
        ...(validatedData.targetRegions && { targetRegions: validatedData.targetRegions }),
        ...(validatedData.targetCategories && { targetCategories: validatedData.targetCategories }),
        ...(validatedData.startDate && { startDate: new Date(validatedData.startDate) }),
        ...(validatedData.endDate && { endDate: new Date(validatedData.endDate) }),
        updatedAt: new Date(),
      },
    })

    return NextResponse.json({
      message: "Campaign updated successfully",
      campaign: updatedCampaign,
    })
  } catch (error) {
    console.error("Campaign update error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid request data", errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// DELETE /api/advertiser/campaigns/[id] - Delete campaign
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    if (session.user.role !== "ADVERTISER") {
      return NextResponse.json(
        { message: "Only advertisers can access this endpoint" },
        { status: 403 }
      )
    }

    // Get campaign and verify ownership
    const campaign = await prisma.campaign.findUnique({
      where: { id: params.id },
      include: {
        advertiser: {
          include: {
            user: true,
          },
        },
      },
    })

    if (!campaign) {
      return NextResponse.json(
        { message: "Campaign not found" },
        { status: 404 }
      )
    }

    // Check if user owns this campaign
    if (campaign.advertiser.user.id !== session.user.id) {
      return NextResponse.json(
        { message: "Forbidden" },
        { status: 403 }
      )
    }

    // Check if campaign can be deleted (only draft campaigns)
    if (campaign.status !== "DRAFT") {
      return NextResponse.json(
        { message: "Only draft campaigns can be deleted. Use cancel instead." },
        { status: 400 }
      )
    }

    // Delete campaign (this will cascade delete ads and placements)
    await prisma.campaign.delete({
      where: { id: params.id },
    })

    return NextResponse.json({
      message: "Campaign deleted successfully",
    })
  } catch (error) {
    console.error("Campaign deletion error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
