# Ad Network Platform

A comprehensive serverless ad network platform built with Next.js that connects publishers and advertisers through intelligent real-time bidding.

## 🚀 Features

### Authentication & Role Management
- **NextAuth Integration**: Secure authentication with credentials provider
- **Role-Based Access**: Publisher, Advertiser, and Admin roles
- **Protected Routes**: Middleware-based route protection
- **Onboarding Flow**: Role-specific onboarding process

### For Publishers
- **Website Monetization**: Easy integration with JavaScript SDK
- **API Key Management**: Secure encrypted API keys and secrets
- **Traffic Analytics**: Monitor impressions, clicks, and earnings
- **Multiple Ad Formats**: Banner, video, native, and popup ads
- **Comprehensive Dashboard**: Real-time analytics with interactive charts
- **Ad Spaces Management**: Create, configure, and monitor ad placements
- **Earnings Tracking**: Detailed revenue analytics and payout management
- **Integration Guides**: Step-by-step guides for HTML, React, and WordPress

### For Advertisers
- **Campaign Management**: Create and manage ad campaigns
- **Flexible Pricing**: CPC, CPM, and CPA models
- **Advanced Targeting**: Region and category-based targeting
- **Real-time Analytics**: Track performance and ROI
- **Comprehensive Dashboard**: Campaign overview with performance metrics
- **Campaign Creation Wizard**: Step-by-step campaign setup with targeting
- **Ad Creative Management**: Upload and manage banner, video, native, and popup ads
- **Advanced Analytics**: Detailed performance insights with interactive charts

### Technical Stack
- **Frontend**: Next.js 15 with App Router, TypeScript, Tailwind CSS
- **UI Components**: ShadCN UI with Radix primitives
- **State Management**: Redux Toolkit
- **Database**: MongoDB with Prisma ORM
- **Authentication**: NextAuth.js
- **Security**: Encrypted credentials, rate limiting, fraud protection

## 🛠 Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd ad-network-web-app
```

2. **Install dependencies**
```bash
npm install --legacy-peer-deps
```

3. **Set up environment variables**
Create a `.env.local` file in the root directory:
```env
# Database
DATABASE_URL="mongodb://localhost:27017/ad-network"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-change-in-production"

# API Keys Encryption
API_KEY_SECRET="your-api-key-encryption-secret-here"

# App Configuration
APP_NAME="Ad Network Platform"
APP_URL="http://localhost:3000"
```

4. **Generate Prisma client**
```bash
npx prisma generate
```

5. **Run the development server**
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Dashboard pages
│   └── onboarding/        # Onboarding flows
├── components/            # React components
│   ├── providers/         # Context providers
│   └── ui/               # ShadCN UI components
├── lib/                   # Utility libraries
│   ├── features/         # Redux slices
│   ├── auth.ts           # NextAuth configuration
│   ├── crypto.ts         # Encryption utilities
│   ├── prisma.ts         # Prisma client
│   └── store.ts          # Redux store
└── middleware.ts          # Route protection middleware
```

## 🔐 Authentication Flow

1. **Registration**: Users sign up and select their role (Publisher/Advertiser)
2. **Onboarding**: Role-specific profile setup
3. **Dashboard Access**: Redirected to appropriate dashboard based on role
4. **API Keys**: Publishers receive encrypted API keys for website integration

## 🎯 Current Implementation Status

### ✅ Completed
- Authentication system with NextAuth
- User registration and role selection
- Publisher and Advertiser onboarding flows
- Database schema with Prisma and MongoDB
- Redux state management setup
- Route protection middleware
- Landing page with feature showcase
- Responsive UI with ShadCN components
- **Publisher Dashboard with full analytics**
- **Ad Spaces management system**
- **Earnings tracking and payout management**
- **API Keys management with integration guides**
- **Comprehensive settings and profile management**
- **Advertiser Dashboard with campaign management**
- **Campaign creation wizard with targeting**
- **Ad creative management system**
- **Advanced analytics with interactive charts**

### 🚧 Next Steps
- Real-time bidding algorithm
- JavaScript SDK for ad serving
- Payment processing integration
- Admin dashboard
- Advanced targeting and optimization features

## 🔧 Development

### Database Schema
The application uses MongoDB with Prisma ORM. Key models include:
- **User**: Base user information with role
- **PublisherProfile**: Website details and API credentials
- **AdvertiserProfile**: Company information and budget
- **Campaign**: Ad campaigns with targeting options
- **AdSpace**: Publisher ad placement areas
- **AdPlacement**: Active ad placements with metrics

### API Routes
- `/api/auth/*`: NextAuth authentication endpoints
- `/api/auth/register`: User registration
- `/api/onboarding/publisher`: Publisher profile creation
- `/api/onboarding/advertiser`: Advertiser profile creation

### Security Features
- Password hashing with bcryptjs
- API key encryption with crypto-js
- Route protection with middleware
- Session-based authentication
- CSRF protection via NextAuth

## 📝 License

This project is private and proprietary.

## 🤝 Contributing

This is a private project. Please contact the development team for contribution guidelines.
#   A d - N e t w o r k - N e w - W e b - A p p 
 
 